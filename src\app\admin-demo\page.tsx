"use client"

import { useState } from "react"
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { toast } from "sonner"
import { 
  Mail, 
  ShoppingBag, 
  Settings, 
  Download, 
  Trash2, 
  ArrowUpDown,
  Users
} from "lucide-react"

// Demo data
const demoContactRequests = [
  {
    id: "1",
    name: "<PERSON>",
    email: "<EMAIL>",
    message: "I'm interested in your premium templates. Can you provide more information about pricing?",
    created_at: "2024-01-15T10:30:00Z"
  },
  {
    id: "2", 
    name: "<PERSON>",
    email: "<EMAIL>",
    message: "I need a custom template for my e-commerce business. Please contact me.",
    created_at: "2024-01-14T15:45:00Z"
  }
]

const demoPurchases = [
  {
    id: "1",
    user_id: "user1",
    template_id: "template1",
    amount: 2999,
    currency: "INR",
    razorpay_payment_id: "pay_123456789",
    status: "completed",
    created_at: "2024-01-15T12:00:00Z",
    templates: { title: "Modern Business Template", category: "Business" },
    profiles: { full_name: "John Doe" }
  },
  {
    id: "2",
    user_id: "user2", 
    template_id: "template2",
    amount: 1999,
    currency: "INR",
    razorpay_payment_id: "pay_987654321",
    status: "completed",
    created_at: "2024-01-14T09:30:00Z",
    templates: { title: "Creative Portfolio", category: "Portfolio" },
    profiles: { full_name: "Jane Smith" }
  }
]

const demoCustomizations = [
  {
    id: "1",
    user_id: "user1",
    navbar_style: "modern",
    hero_section: "hero1",
    footer_style: "simple",
    created_at: "2024-01-15T14:20:00Z",
    updated_at: "2024-01-15T14:20:00Z",
    profiles: { full_name: "John Doe" }
  },
  {
    id: "2",
    user_id: "user2",
    navbar_style: "classic",
    hero_section: "hero2", 
    footer_style: "detailed",
    created_at: "2024-01-14T11:15:00Z",
    updated_at: "2024-01-14T11:15:00Z",
    profiles: { full_name: "Jane Smith" }
  }
]

const demoVisitorLogs = [
  {
    id: "1",
    ip_address: "*************",
    path: "/",
    user_agent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
    created_at: "2024-01-15T16:30:00Z"
  },
  {
    id: "2",
    ip_address: "*************", 
    path: "/templates",
    user_agent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36",
    created_at: "2024-01-15T16:25:00Z"
  },
  {
    id: "3",
    ip_address: "*************",
    path: "/customize",
    user_agent: "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36",
    created_at: "2024-01-15T16:20:00Z"
  }
]

export default function AdminDemoPage() {
  const [activeTab, setActiveTab] = useState("contacts")

  const exportToCSV = (data: any[], filename: string, headers: string[]) => {
    const csvData = [
      headers,
      ...data.map(item => headers.map(header => {
        // Simple mapping for demo
        switch(header) {
          case 'Name': return item.name || item.profiles?.full_name || 'Unknown'
          case 'Email': return item.email || 'N/A'
          case 'Message': return item.message || 'N/A'
          case 'User Email': return item.profiles?.full_name || 'Unknown'
          case 'Template': return item.templates?.title || 'N/A'
          case 'Amount': return item.amount?.toString() || 'N/A'
          case 'Currency': return item.currency || 'N/A'
          case 'Status': return item.status || 'N/A'
          case 'Payment ID': return item.razorpay_payment_id || 'N/A'
          case 'User': return item.profiles?.full_name || 'Unknown'
          case 'Navbar Style': return item.navbar_style || 'N/A'
          case 'Hero Section': return item.hero_section || 'N/A'
          case 'Footer Style': return item.footer_style || 'N/A'
          case 'IP Address': return item.ip_address || 'Unknown'
          case 'Path': return item.path || 'N/A'
          case 'User Agent': return item.user_agent || 'Unknown'
          case 'Created At': return new Date(item.created_at).toLocaleString()
          case 'Updated At': return new Date(item.updated_at || item.created_at).toLocaleString()
          default: return 'N/A'
        }
      }))
    ]

    const csvContent = csvData.map(row => 
      row.map(field => `"${field}"`).join(',')
    ).join('\n')

    const blob = new Blob([csvContent], { type: 'text/csv' })
    const url = window.URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `${filename}-${new Date().toISOString().split('T')[0]}.csv`
    a.click()
    window.URL.revokeObjectURL(url)
    
    toast.success(`${filename} exported successfully!`)
  }

  const handleDelete = (id: string, type: string) => {
    toast.success(`${type} deleted successfully! (Demo mode)`)
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Admin Panel (Demo)</h1>
        <p className="text-muted-foreground">
          This is a demo of the admin panel functionality with sample data
        </p>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Contact Requests</CardTitle>
            <Mail className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{demoContactRequests.length}</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Purchases</CardTitle>
            <ShoppingBag className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{demoPurchases.length}</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Customizations</CardTitle>
            <Settings className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{demoCustomizations.length}</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Visitor Logs</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{demoVisitorLogs.length}</div>
          </CardContent>
        </Card>
      </div>

      {/* Admin Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="contacts">Contact Requests</TabsTrigger>
          <TabsTrigger value="purchases">Purchases</TabsTrigger>
          <TabsTrigger value="customizations">Customizations</TabsTrigger>
          <TabsTrigger value="visitors">Visitor Logs</TabsTrigger>
        </TabsList>

        <TabsContent value="contacts" className="space-y-4">
          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <div>
                  <CardTitle>Contact Requests</CardTitle>
                  <CardDescription>Manage customer inquiries and messages</CardDescription>
                </div>
                <div className="flex gap-2">
                  <Button 
                    variant="outline" 
                    onClick={() => exportToCSV(demoContactRequests, 'contact-requests', ['Name', 'Email', 'Message', 'Created At'])}
                  >
                    <Download className="h-4 w-4 mr-2" />
                    Export CSV
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {demoContactRequests.map((request) => (
                  <div key={request.id} className="border rounded-lg p-4">
                    <div className="flex justify-between items-start mb-2">
                      <div>
                        <h4 className="font-semibold">{request.name}</h4>
                        <p className="text-sm text-muted-foreground">{request.email}</p>
                      </div>
                      <div className="flex gap-2">
                        <Button variant="outline" size="sm">
                          <ArrowUpDown className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="destructive"
                          size="sm"
                          onClick={() => handleDelete(request.id, 'Contact request')}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                    <p className="text-sm mb-2">{request.message}</p>
                    <p className="text-xs text-muted-foreground">
                      {new Date(request.created_at).toLocaleString()}
                    </p>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="purchases" className="space-y-4">
          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <div>
                  <CardTitle>Purchases</CardTitle>
                  <CardDescription>
                    Total Revenue: ₹{demoPurchases.reduce((sum, p) => sum + p.amount, 0)} • {demoPurchases.length} purchases
                  </CardDescription>
                </div>
                <div className="flex gap-2">
                  <Button 
                    variant="outline" 
                    onClick={() => exportToCSV(demoPurchases, 'purchases', ['User Email', 'Template', 'Amount', 'Currency', 'Status', 'Payment ID', 'Created At'])}
                  >
                    <Download className="h-4 w-4 mr-2" />
                    Export CSV
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {demoPurchases.map((purchase) => (
                  <div key={purchase.id} className="border rounded-lg p-4">
                    <div className="flex justify-between items-start mb-2">
                      <div>
                        <h4 className="font-semibold">{purchase.templates.title}</h4>
                        <p className="text-sm text-muted-foreground">{purchase.profiles.full_name}</p>
                      </div>
                      <div className="flex gap-2">
                        <Button variant="outline" size="sm">
                          <ArrowUpDown className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="destructive"
                          size="sm"
                          onClick={() => handleDelete(purchase.id, 'Purchase')}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                      <div>
                        <span className="text-muted-foreground">Amount:</span>
                        <p className="font-medium">₹{purchase.amount}</p>
                      </div>
                      <div>
                        <span className="text-muted-foreground">Status:</span>
                        <Badge variant="default">{purchase.status}</Badge>
                      </div>
                      <div>
                        <span className="text-muted-foreground">Payment ID:</span>
                        <p className="font-mono text-xs">{purchase.razorpay_payment_id}</p>
                      </div>
                      <div>
                        <span className="text-muted-foreground">Date:</span>
                        <p>{new Date(purchase.created_at).toLocaleDateString()}</p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="customizations" className="space-y-4">
          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <div>
                  <CardTitle>Customizations</CardTitle>
                  <CardDescription>User template customization sessions</CardDescription>
                </div>
                <div className="flex gap-2">
                  <Button 
                    variant="outline" 
                    onClick={() => exportToCSV(demoCustomizations, 'customizations', ['User', 'Navbar Style', 'Hero Section', 'Footer Style', 'Created At', 'Updated At'])}
                  >
                    <Download className="h-4 w-4 mr-2" />
                    Export CSV
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {demoCustomizations.map((customization) => (
                  <div key={customization.id} className="border rounded-lg p-4">
                    <div className="flex justify-between items-start mb-2">
                      <div>
                        <h4 className="font-semibold">{customization.profiles.full_name}</h4>
                        <p className="text-sm text-muted-foreground">ID: {customization.id}</p>
                      </div>
                      <div className="flex gap-2">
                        <Button variant="outline" size="sm">
                          <ArrowUpDown className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="destructive"
                          size="sm"
                          onClick={() => handleDelete(customization.id, 'Customization')}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                      <div>
                        <span className="text-muted-foreground">Navbar:</span>
                        <p className="font-medium">{customization.navbar_style}</p>
                      </div>
                      <div>
                        <span className="text-muted-foreground">Hero:</span>
                        <p className="font-medium">{customization.hero_section}</p>
                      </div>
                      <div>
                        <span className="text-muted-foreground">Footer:</span>
                        <p className="font-medium">{customization.footer_style}</p>
                      </div>
                    </div>
                    <div className="mt-2 text-xs text-muted-foreground">
                      Created: {new Date(customization.created_at).toLocaleString()} • 
                      Updated: {new Date(customization.updated_at).toLocaleString()}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="visitors" className="space-y-4">
          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <div>
                  <CardTitle>Visitor Logs</CardTitle>
                  <CardDescription>
                    {demoVisitorLogs.length} visits • {new Set(demoVisitorLogs.map(log => log.ip_address)).size} unique IPs
                  </CardDescription>
                </div>
                <div className="flex gap-2">
                  <Button 
                    variant="outline" 
                    onClick={() => exportToCSV(demoVisitorLogs, 'visitor-logs', ['IP Address', 'Path', 'User Agent', 'Created At'])}
                  >
                    <Download className="h-4 w-4 mr-2" />
                    Export CSV
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {demoVisitorLogs.map((log) => (
                  <div key={log.id} className="border rounded-lg p-4">
                    <div className="flex justify-between items-start mb-2">
                      <div>
                        <h4 className="font-semibold">{log.path}</h4>
                        <p className="text-sm text-muted-foreground">IP: {log.ip_address}</p>
                      </div>
                      <div className="flex gap-2">
                        <Button variant="outline" size="sm">
                          <ArrowUpDown className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="destructive"
                          size="sm"
                          onClick={() => handleDelete(log.id, 'Visitor log')}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                    <div className="text-sm">
                      <p className="text-muted-foreground mb-1">User Agent:</p>
                      <p className="font-mono text-xs break-all">{log.user_agent}</p>
                    </div>
                    <div className="mt-2 text-xs text-muted-foreground">
                      {new Date(log.created_at).toLocaleString()}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
