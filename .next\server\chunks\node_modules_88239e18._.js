module.exports = {

"[project]/node_modules/@supabase/node-fetch/lib/index.js [app-route] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/@supabase/node-fetch/lib/index.js [app-route] (ecmascript)");
    });
});
}}),
"[project]/node_modules/ws/wrapper.mjs [app-route] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/node_modules_ws_daabdc74._.js",
  "server/chunks/[root-of-the-server]__b8136ca7._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/ws/wrapper.mjs [app-route] (ecmascript)");
    });
});
}}),

};