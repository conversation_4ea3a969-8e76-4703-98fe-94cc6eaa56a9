{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 108, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Documents/kaleidonex/src/lib/supabase/server.ts"], "sourcesContent": ["import { createServerClient } from '@supabase/ssr'\nimport { cookies } from 'next/headers'\n\nexport async function createClient() {\n  const cookieStore = await cookies()\n\n  const url = process.env.NEXT_PUBLIC_SUPABASE_URL\n  const key = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY\n\n  if (!url || !key) {\n    throw new Error('Missing Supabase environment variables. Please check your .env.local file.')\n  }\n\n  return createServerClient(\n    url,\n    key,\n    {\n      cookies: {\n        getAll() {\n          return cookieStore.getAll()\n        },\n        setAll(cookiesToSet) {\n          try {\n            cookiesToSet.forEach(({ name, value, options }) =>\n              cookieStore.set(name, value, options)\n            )\n          } catch {\n            // The `setAll` method was called from a Server Component.\n            // This can be ignored if you have middleware refreshing\n            // user sessions.\n          }\n        },\n      },\n    }\n  )\n}\n"], "names": [], "mappings": ";;;AAAA;AAAA;AACA;;;AAEO,eAAe;IACpB,MAAM,cAAc,MAAM,CAAA,GAAA,iIAAA,CAAA,UAAO,AAAD;IAEhC,MAAM,MAAM,QAAQ,GAAG,CAAC,wBAAwB;IAChD,MAAM,MAAM,QAAQ,GAAG,CAAC,6BAA6B;IAErD,IAAI,CAAC,OAAO,CAAC,KAAK;QAChB,MAAM,IAAI,MAAM;IAClB;IAEA,OAAO,CAAA,GAAA,2KAAA,CAAA,qBAAkB,AAAD,EACtB,KACA,KACA;QACE,SAAS;YACP;gBACE,OAAO,YAAY,MAAM;YAC3B;YACA,QAAO,YAAY;gBACjB,IAAI;oBACF,aAAa,OAAO,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,GAC5C,YAAY,GAAG,CAAC,MAAM,OAAO;gBAEjC,EAAE,OAAM;gBACN,0DAA0D;gBAC1D,wDAAwD;gBACxD,iBAAiB;gBACnB;YACF;QACF;IACF;AAEJ", "debugId": null}}, {"offset": {"line": 146, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Documents/kaleidonex/src/app/api/track-visitor/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { createClient } from '@/lib/supabase/server'\n\nexport async function POST(request: NextRequest) {\n  try {\n    const { path, userAgent } = await request.json()\n    \n    // Get IP address from request headers\n    const forwarded = request.headers.get('x-forwarded-for')\n    const realIp = request.headers.get('x-real-ip')\n    const ip = forwarded?.split(',')[0] || realIp || request.ip || 'unknown'\n\n    const supabase = await createClient()\n    \n    const { error } = await supabase\n      .from('visitor_logs')\n      .insert({\n        ip_address: ip,\n        path,\n        user_agent: userAgent,\n      })\n\n    if (error) {\n      console.error('Error tracking visitor:', error)\n      return NextResponse.json(\n        { error: 'Failed to track visitor' },\n        { status: 500 }\n      )\n    }\n\n    return NextResponse.json({ success: true })\n  } catch (error) {\n    console.error('Error in visitor tracking:', error)\n    return NextResponse.json(\n      { error: 'Internal server error' },\n      { status: 500 }\n    )\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,GAAG,MAAM,QAAQ,IAAI;QAE9C,sCAAsC;QACtC,MAAM,YAAY,QAAQ,OAAO,CAAC,GAAG,CAAC;QACtC,MAAM,SAAS,QAAQ,OAAO,CAAC,GAAG,CAAC;QACnC,MAAM,KAAK,WAAW,MAAM,IAAI,CAAC,EAAE,IAAI,UAAU,QAAQ,EAAE,IAAI;QAE/D,MAAM,WAAW,MAAM,CAAA,GAAA,kIAAA,CAAA,eAAY,AAAD;QAElC,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SACrB,IAAI,CAAC,gBACL,MAAM,CAAC;YACN,YAAY;YACZ;YACA,YAAY;QACd;QAEF,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,2BAA2B;YACzC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAA0B,GACnC;gBAAE,QAAQ;YAAI;QAElB;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,SAAS;QAAK;IAC3C,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,8BAA8B;QAC5C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAwB,GACjC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}