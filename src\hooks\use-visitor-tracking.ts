"use client"

import { useEffect } from 'react'
import { usePathname } from 'next/navigation'

export function useVisitorTracking() {
  const pathname = usePathname()

  useEffect(() => {
    const trackVisitor = async () => {
      try {
        await fetch('/api/track-visitor', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            path: pathname,
            userAgent: navigator.userAgent,
            timestamp: new Date().toISOString(),
          }),
        })
      } catch (error) {
        // Silently fail - visitor tracking shouldn't break the app
        console.warn('Failed to track visitor:', error)
      }
    }

    trackVisitor()
  }, [pathname])
}
