{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 164, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Documents/kaleidonex/src/lib/razorpay.ts"], "sourcesContent": ["import Razorpay from 'razorpay'\n\n// Server-side Razorpay instance\nexport const razorpay = process.env.NEXT_PUBLIC_RAZORPAY_KEY_ID && process.env.RAZORPAY_KEY_SECRET\n  ? new Razorpay({\n      key_id: process.env.NEXT_PUBLIC_RAZORPAY_KEY_ID!,\n      key_secret: process.env.RAZORPAY_KEY_SECRET!,\n    })\n  : null\n\n// Client-side Razorpay configuration\nexport const razorpayConfig = {\n  key: process.env.NEXT_PUBLIC_RAZORPAY_KEY_ID!,\n}\n\n// Payment types\nexport interface PaymentData {\n  amount: number // in paise (smallest currency unit)\n  currency: string\n  receipt: string\n  notes?: Record<string, string>\n}\n\nexport interface RazorpayOptions {\n  key: string\n  amount: number\n  currency: string\n  name: string\n  description?: string\n  order_id: string\n  handler: (response: RazorpayResponse) => void\n  prefill?: {\n    name?: string\n    email?: string\n    contact?: string\n  }\n  theme?: {\n    color?: string\n  }\n}\n\nexport interface RazorpayResponse {\n  razorpay_payment_id: string\n  razorpay_order_id: string\n  razorpay_signature: string\n}\n"], "names": [], "mappings": ";;;;AAAA;;AAGO,MAAM,WAAW,4DAA2C,QAAQ,GAAG,CAAC,mBAAmB,GAC9F,IAAI,8IAAA,CAAA,UAAQ,CAAC;IACX,MAAM;IACN,YAAY,QAAQ,GAAG,CAAC,mBAAmB;AAC7C,KACA;AAGG,MAAM,iBAAiB;IAC5B,GAAG;AACL", "debugId": null}}, {"offset": {"line": 191, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Documents/kaleidonex/src/lib/supabase/server.ts"], "sourcesContent": ["import { createServerClient } from '@supabase/ssr'\nimport { cookies } from 'next/headers'\n\nexport async function createClient() {\n  const cookieStore = await cookies()\n\n  const url = process.env.NEXT_PUBLIC_SUPABASE_URL\n  const key = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY\n\n  if (!url || !key) {\n    throw new Error('Missing Supabase environment variables. Please check your .env.local file.')\n  }\n\n  return createServerClient(\n    url,\n    key,\n    {\n      cookies: {\n        getAll() {\n          return cookieStore.getAll()\n        },\n        setAll(cookiesToSet) {\n          try {\n            cookiesToSet.forEach(({ name, value, options }) =>\n              cookieStore.set(name, value, options)\n            )\n          } catch {\n            // The `setAll` method was called from a Server Component.\n            // This can be ignored if you have middleware refreshing\n            // user sessions.\n          }\n        },\n      },\n    }\n  )\n}\n"], "names": [], "mappings": ";;;AAAA;AAAA;AACA;;;AAEO,eAAe;IACpB,MAAM,cAAc,MAAM,CAAA,GAAA,iIAAA,CAAA,UAAO,AAAD;IAEhC,MAAM;IACN,MAAM;IAEN,uCAAkB;;IAElB;IAEA,OAAO,CAAA,GAAA,2KAAA,CAAA,qBAAkB,AAAD,EACtB,KACA,KACA;QACE,SAAS;YACP;gBACE,OAAO,YAAY,MAAM;YAC3B;YACA,QAAO,YAAY;gBACjB,IAAI;oBACF,aAAa,OAAO,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,GAC5C,YAAY,GAAG,CAAC,MAAM,OAAO;gBAEjC,EAAE,OAAM;gBACN,0DAA0D;gBAC1D,wDAAwD;gBACxD,iBAAiB;gBACnB;YACF;QACF;IACF;AAEJ", "debugId": null}}, {"offset": {"line": 229, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Documents/kaleidonex/src/app/api/payments/create-order/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { razorpay } from '@/lib/razorpay'\nimport { createClient } from '@/lib/supabase/server'\n\nexport async function POST(request: NextRequest) {\n  try {\n    const { amount, currency = 'INR', receipt, templateId } = await request.json()\n\n    // Validate required fields\n    if (!amount || !receipt) {\n      return NextResponse.json(\n        { error: 'Amount and receipt are required' },\n        { status: 400 }\n      )\n    }\n\n    // Check if Razorpay is configured\n    if (!razorpay) {\n      return NextResponse.json(\n        { error: 'Payment system not configured. Please contact support.' },\n        { status: 500 }\n      )\n    }\n\n    // Create Razorpay order\n    const order = await razorpay.orders.create({\n      amount: amount * 100, // Convert to paise\n      currency,\n      receipt,\n      payment_capture: 1,\n    })\n\n    // Get user from Supabase\n    const supabase = await createClient()\n    const { data: { user }, error: userError } = await supabase.auth.getUser()\n\n    if (userError || !user) {\n      return NextResponse.json(\n        { error: 'User not authenticated' },\n        { status: 401 }\n      )\n    }\n\n    // Save order to database\n    const { error: dbError } = await supabase\n      .from('orders')\n      .insert({\n        user_id: user.id,\n        amount: amount,\n        currency,\n        status: 'created',\n        razorpay_order_id: order.id,\n      })\n\n    if (dbError) {\n      console.error('Database error:', dbError)\n      // Continue even if DB save fails, as Razorpay order is created\n    }\n\n    return NextResponse.json({\n      orderId: order.id,\n      amount: order.amount,\n      currency: order.currency,\n    })\n  } catch (error) {\n    console.error('Error creating order:', error)\n    return NextResponse.json(\n      { error: 'Failed to create order' },\n      { status: 500 }\n    )\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,EAAE,MAAM,EAAE,WAAW,KAAK,EAAE,OAAO,EAAE,UAAU,EAAE,GAAG,MAAM,QAAQ,IAAI;QAE5E,2BAA2B;QAC3B,IAAI,CAAC,UAAU,CAAC,SAAS;YACvB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAkC,GAC3C;gBAAE,QAAQ;YAAI;QAElB;QAEA,kCAAkC;QAClC,IAAI,CAAC,wHAAA,CAAA,WAAQ,EAAE;YACb,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAyD,GAClE;gBAAE,QAAQ;YAAI;QAElB;QAEA,wBAAwB;QACxB,MAAM,QAAQ,MAAM,wHAAA,CAAA,WAAQ,CAAC,MAAM,CAAC,MAAM,CAAC;YACzC,QAAQ,SAAS;YACjB;YACA;YACA,iBAAiB;QACnB;QAEA,yBAAyB;QACzB,MAAM,WAAW,MAAM,CAAA,GAAA,kIAAA,CAAA,eAAY,AAAD;QAClC,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;QAExE,IAAI,aAAa,CAAC,MAAM;YACtB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAyB,GAClC;gBAAE,QAAQ;YAAI;QAElB;QAEA,yBAAyB;QACzB,MAAM,EAAE,OAAO,OAAO,EAAE,GAAG,MAAM,SAC9B,IAAI,CAAC,UACL,MAAM,CAAC;YACN,SAAS,KAAK,EAAE;YAChB,QAAQ;YACR;YACA,QAAQ;YACR,mBAAmB,MAAM,EAAE;QAC7B;QAEF,IAAI,SAAS;YACX,QAAQ,KAAK,CAAC,mBAAmB;QACjC,+DAA+D;QACjE;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS,MAAM,EAAE;YACjB,QAAQ,MAAM,MAAM;YACpB,UAAU,MAAM,QAAQ;QAC1B;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yBAAyB;QACvC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAyB,GAClC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}