{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Documents/kaleidonex/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 104, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Documents/kaleidonex/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 150, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Documents/kaleidonex/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 224, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Documents/kaleidonex/src/lib/supabase/client.ts"], "sourcesContent": ["import { createBrowserClient } from '@supabase/ssr'\n\nexport function createClient() {\n  return createBrowserClient(\n    process.env.NEXT_PUBLIC_SUPABASE_URL!,\n    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!\n  )\n}\n"], "names": [], "mappings": ";;;AAAA;AAAA;;AAEO,SAAS;IACd,OAAO,CAAA,GAAA,0KAAA,CAAA,sBAAmB,AAAD,EACvB,QAAQ,GAAG,CAAC,wBAAwB,EACpC,QAAQ,GAAG,CAAC,6BAA6B;AAE7C", "debugId": null}}, {"offset": {"line": 303, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Documents/kaleidonex/src/lib/razorpay.ts"], "sourcesContent": ["import Razorpay from 'razorpay'\n\n// Server-side Razorpay instance\nexport const razorpay = new Razorpay({\n  key_id: process.env.NEXT_PUBLIC_RAZORPAY_KEY_ID!,\n  key_secret: process.env.RAZORPAY_KEY_SECRET!,\n})\n\n// Client-side Razorpay configuration\nexport const razorpayConfig = {\n  key: process.env.NEXT_PUBLIC_RAZORPAY_KEY_ID!,\n}\n\n// Payment types\nexport interface PaymentData {\n  amount: number // in paise (smallest currency unit)\n  currency: string\n  receipt: string\n  notes?: Record<string, string>\n}\n\nexport interface RazorpayOptions {\n  key: string\n  amount: number\n  currency: string\n  name: string\n  description?: string\n  order_id: string\n  handler: (response: RazorpayResponse) => void\n  prefill?: {\n    name?: string\n    email?: string\n    contact?: string\n  }\n  theme?: {\n    color?: string\n  }\n}\n\nexport interface RazorpayResponse {\n  razorpay_payment_id: string\n  razorpay_order_id: string\n  razorpay_signature: string\n}\n"], "names": [], "mappings": ";;;;AAAA;;AAGO,MAAM,WAAW,IAAI,4IAAA,CAAA,UAAQ,CAAC;IACnC,QAAQ,QAAQ,GAAG,CAAC,2BAA2B;IAC/C,YAAY,QAAQ,GAAG,CAAC,mBAAmB;AAC7C;AAGO,MAAM,iBAAiB;IAC5B,KAAK,QAAQ,GAAG,CAAC,2BAA2B;AAC9C", "debugId": null}}, {"offset": {"line": 322, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Documents/kaleidonex/src/components/payment/razorpay-button.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState } from \"react\"\nimport { But<PERSON> } from \"@/components/ui/button\"\nimport { razorpayConfig, type RazorpayOptions, type RazorpayResponse } from \"@/lib/razorpay\"\n\ninterface RazorpayButtonProps {\n  amount: number\n  currency?: string\n  description?: string\n  onSuccess?: (response: RazorpayResponse) => void\n  onError?: (error: any) => void\n  disabled?: boolean\n  children?: React.ReactNode\n}\n\ndeclare global {\n  interface Window {\n    Razorpay: any\n  }\n}\n\nexport function RazorpayButton({\n  amount,\n  currency = \"INR\",\n  description = \"Payment\",\n  onSuccess,\n  onError,\n  disabled = false,\n  children = \"Pay Now\",\n}: RazorpayButtonProps) {\n  const [loading, setLoading] = useState(false)\n\n  const handlePayment = async () => {\n    try {\n      setLoading(true)\n\n      // Create order on server\n      const orderResponse = await fetch(\"/api/payments/create-order\", {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\",\n        },\n        body: JSON.stringify({\n          amount,\n          currency,\n          receipt: `receipt_${Date.now()}`,\n        }),\n      })\n\n      if (!orderResponse.ok) {\n        throw new Error(\"Failed to create order\")\n      }\n\n      const orderData = await orderResponse.json()\n\n      // Load Razorpay script if not already loaded\n      if (!window.Razorpay) {\n        const script = document.createElement(\"script\")\n        script.src = \"https://checkout.razorpay.com/v1/checkout.js\"\n        script.async = true\n        document.body.appendChild(script)\n        \n        await new Promise((resolve) => {\n          script.onload = resolve\n        })\n      }\n\n      // Configure Razorpay options\n      const options: RazorpayOptions = {\n        key: razorpayConfig.key,\n        amount: orderData.amount,\n        currency: orderData.currency,\n        name: \"KaleidoneX\",\n        description,\n        order_id: orderData.orderId,\n        handler: async (response: RazorpayResponse) => {\n          try {\n            // Verify payment on server\n            const verifyResponse = await fetch(\"/api/payments/verify\", {\n              method: \"POST\",\n              headers: {\n                \"Content-Type\": \"application/json\",\n              },\n              body: JSON.stringify(response),\n            })\n\n            if (verifyResponse.ok) {\n              onSuccess?.(response)\n            } else {\n              throw new Error(\"Payment verification failed\")\n            }\n          } catch (error) {\n            onError?.(error)\n          }\n        },\n        prefill: {\n          name: \"\",\n          email: \"\",\n          contact: \"\",\n        },\n        theme: {\n          color: \"#3B82F6\",\n        },\n      }\n\n      // Open Razorpay checkout\n      const razorpay = new window.Razorpay(options)\n      razorpay.open()\n\n      razorpay.on(\"payment.failed\", (response: any) => {\n        onError?.(response.error)\n      })\n    } catch (error) {\n      onError?.(error)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  return (\n    <Button\n      onClick={handlePayment}\n      disabled={disabled || loading}\n      className=\"w-full\"\n    >\n      {loading ? \"Processing...\" : children}\n    </Button>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAsBO,SAAS,eAAe,EAC7B,MAAM,EACN,WAAW,KAAK,EAChB,cAAc,SAAS,EACvB,SAAS,EACT,OAAO,EACP,WAAW,KAAK,EAChB,WAAW,SAAS,EACA;IACpB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,MAAM,gBAAgB;QACpB,IAAI;YACF,WAAW;YAEX,yBAAyB;YACzB,MAAM,gBAAgB,MAAM,MAAM,8BAA8B;gBAC9D,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB;oBACA;oBACA,SAAS,CAAC,QAAQ,EAAE,KAAK,GAAG,IAAI;gBAClC;YACF;YAEA,IAAI,CAAC,cAAc,EAAE,EAAE;gBACrB,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,YAAY,MAAM,cAAc,IAAI;YAE1C,6CAA6C;YAC7C,IAAI,CAAC,OAAO,QAAQ,EAAE;gBACpB,MAAM,SAAS,SAAS,aAAa,CAAC;gBACtC,OAAO,GAAG,GAAG;gBACb,OAAO,KAAK,GAAG;gBACf,SAAS,IAAI,CAAC,WAAW,CAAC;gBAE1B,MAAM,IAAI,QAAQ,CAAC;oBACjB,OAAO,MAAM,GAAG;gBAClB;YACF;YAEA,6BAA6B;YAC7B,MAAM,UAA2B;gBAC/B,KAAK,sHAAA,CAAA,iBAAc,CAAC,GAAG;gBACvB,QAAQ,UAAU,MAAM;gBACxB,UAAU,UAAU,QAAQ;gBAC5B,MAAM;gBACN;gBACA,UAAU,UAAU,OAAO;gBAC3B,SAAS,OAAO;oBACd,IAAI;wBACF,2BAA2B;wBAC3B,MAAM,iBAAiB,MAAM,MAAM,wBAAwB;4BACzD,QAAQ;4BACR,SAAS;gCACP,gBAAgB;4BAClB;4BACA,MAAM,KAAK,SAAS,CAAC;wBACvB;wBAEA,IAAI,eAAe,EAAE,EAAE;4BACrB,YAAY;wBACd,OAAO;4BACL,MAAM,IAAI,MAAM;wBAClB;oBACF,EAAE,OAAO,OAAO;wBACd,UAAU;oBACZ;gBACF;gBACA,SAAS;oBACP,MAAM;oBACN,OAAO;oBACP,SAAS;gBACX;gBACA,OAAO;oBACL,OAAO;gBACT;YACF;YAEA,yBAAyB;YACzB,MAAM,WAAW,IAAI,OAAO,QAAQ,CAAC;YACrC,SAAS,IAAI;YAEb,SAAS,EAAE,CAAC,kBAAkB,CAAC;gBAC7B,UAAU,SAAS,KAAK;YAC1B;QACF,EAAE,OAAO,OAAO;YACd,UAAU;QACZ,SAAU;YACR,WAAW;QACb;IACF;IAEA,qBACE,8OAAC,kIAAA,CAAA,SAAM;QACL,SAAS;QACT,UAAU,YAAY;QACtB,WAAU;kBAET,UAAU,kBAAkB;;;;;;AAGnC", "debugId": null}}, {"offset": {"line": 430, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Documents/kaleidonex/src/app/templates/page.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState, useEffect } from \"react\"\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\"\nimport { <PERSON><PERSON> } from \"@/components/ui/button\"\nimport { Badge } from \"@/components/ui/badge\"\nimport { Input } from \"@/components/ui/input\"\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from \"@/components/ui/select\"\nimport { Search, Eye, ShoppingCart, IndianRupee } from \"lucide-react\"\nimport { createClient } from \"@/lib/supabase/client\"\nimport { Database } from \"@/lib/database.types\"\nimport { RazorpayButton } from \"@/components/payment/razorpay-button\"\nimport { toast } from \"sonner\"\n\ntype Template = Database['public']['Tables']['templates']['Row']\n\nexport default function TemplatesPage() {\n  const [templates, setTemplates] = useState<Template[]>([])\n  const [filteredTemplates, setFilteredTemplates] = useState<Template[]>([])\n  const [loading, setLoading] = useState(true)\n  const [searchTerm, setSearchTerm] = useState(\"\")\n  const [selectedCategory, setSelectedCategory] = useState(\"All\")\n  const [sortBy, setSortBy] = useState(\"title\")\n  const [categories, setCategories] = useState<string[]>([\"All\"])\n\n  const supabase = createClient()\n\n  useEffect(() => {\n    fetchTemplates()\n  }, [])\n\n  useEffect(() => {\n    filterAndSortTemplates()\n  }, [templates, searchTerm, selectedCategory, sortBy])\n\n  const fetchTemplates = async () => {\n    try {\n      const { data, error } = await supabase\n        .from('templates')\n        .select('*')\n        .order('created_at', { ascending: false })\n\n      if (error) throw error\n\n      setTemplates(data || [])\n\n      // Extract unique categories\n      const uniqueCategories = [\"All\", ...new Set(data?.map(t => t.category) || [])]\n      setCategories(uniqueCategories)\n    } catch (error) {\n      console.error('Error fetching templates:', error)\n      toast.error('Failed to load templates')\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const filterAndSortTemplates = () => {\n    let filtered = templates\n\n    // Filter by search term\n    if (searchTerm) {\n      filtered = filtered.filter(template =>\n        template.title.toLowerCase().includes(searchTerm.toLowerCase()) ||\n        template.description?.toLowerCase().includes(searchTerm.toLowerCase())\n      )\n    }\n\n    // Filter by category\n    if (selectedCategory !== \"All\") {\n      filtered = filtered.filter(template => template.category === selectedCategory)\n    }\n\n    // Sort templates\n    filtered.sort((a, b) => {\n      switch (sortBy) {\n        case \"price-low\":\n          return a.price - b.price\n        case \"price-high\":\n          return b.price - a.price\n        case \"title\":\n        default:\n          return a.title.localeCompare(b.title)\n      }\n    })\n\n    setFilteredTemplates(filtered)\n  }\n\n  const handlePurchaseSuccess = (templateId: string) => {\n    toast.success('Template purchased successfully!')\n    // You could redirect to a success page or refresh user's purchases\n  }\n\n  const handlePurchaseError = (error: any) => {\n    toast.error('Payment failed. Please try again.')\n    console.error('Purchase error:', error)\n  }\n\n  if (loading) {\n    return (\n      <div className=\"space-y-6\">\n        <div>\n          <h1 className=\"text-3xl font-bold tracking-tight\">Templates</h1>\n          <p className=\"text-muted-foreground\">Loading templates...</p>\n        </div>\n        <div className=\"grid gap-6 md:grid-cols-2 lg:grid-cols-3\">\n          {[...Array(6)].map((_, i) => (\n            <Card key={i} className=\"overflow-hidden\">\n              <div className=\"aspect-video bg-muted animate-pulse\" />\n              <CardHeader>\n                <div className=\"h-4 bg-muted animate-pulse rounded\" />\n                <div className=\"h-3 bg-muted animate-pulse rounded w-3/4\" />\n              </CardHeader>\n            </Card>\n          ))}\n        </div>\n      </div>\n    )\n  }\n  return (\n    <div className=\"space-y-6\">\n      <div>\n        <h1 className=\"text-3xl font-bold tracking-tight\">Templates</h1>\n        <p className=\"text-muted-foreground\">\n          Choose from our collection of professionally designed templates\n        </p>\n      </div>\n\n      {/* Search and Filter */}\n      <div className=\"flex flex-col sm:flex-row gap-4\">\n        <div className=\"relative flex-1\">\n          <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground\" />\n          <Input\n            placeholder=\"Search templates...\"\n            className=\"pl-10\"\n            value={searchTerm}\n            onChange={(e) => setSearchTerm(e.target.value)}\n          />\n        </div>\n        <div className=\"flex gap-2 flex-wrap\">\n          {categories.map((category) => (\n            <Button\n              key={category}\n              variant={category === selectedCategory ? \"default\" : \"outline\"}\n              size=\"sm\"\n              onClick={() => setSelectedCategory(category)}\n            >\n              {category}\n            </Button>\n          ))}\n        </div>\n        <Select value={sortBy} onValueChange={setSortBy}>\n          <SelectTrigger className=\"w-48\">\n            <SelectValue placeholder=\"Sort by\" />\n          </SelectTrigger>\n          <SelectContent>\n            <SelectItem value=\"title\">Sort by Title</SelectItem>\n            <SelectItem value=\"price-low\">Price: Low to High</SelectItem>\n            <SelectItem value=\"price-high\">Price: High to Low</SelectItem>\n          </SelectContent>\n        </Select>\n      </div>\n\n      {/* Templates Grid */}\n      <div className=\"grid gap-6 md:grid-cols-2 lg:grid-cols-3\">\n        {filteredTemplates.map((template) => (\n          <Card key={template.id} className=\"overflow-hidden\">\n            <div className=\"aspect-video bg-muted relative\">\n              {template.preview_image ? (\n                <img\n                  src={template.preview_image}\n                  alt={template.title}\n                  className=\"w-full h-full object-cover\"\n                />\n              ) : (\n                <div className=\"absolute inset-0 flex items-center justify-center text-muted-foreground\">\n                  Template Preview\n                </div>\n              )}\n            </div>\n            <CardHeader>\n              <div className=\"flex justify-between items-start\">\n                <div>\n                  <CardTitle className=\"text-lg\">{template.title}</CardTitle>\n                  <CardDescription className=\"mt-1\">\n                    {template.description}\n                  </CardDescription>\n                </div>\n                <Badge variant=\"secondary\">{template.category}</Badge>\n              </div>\n            </CardHeader>\n            <CardContent>\n              <div className=\"flex items-center justify-between mb-4\">\n                <div className=\"flex items-center gap-1\">\n                  <IndianRupee className=\"h-4 w-4\" />\n                  <span className=\"text-2xl font-bold\">₹{template.price}</span>\n                </div>\n              </div>\n\n              <div className=\"flex gap-2\">\n                {template.preview_url && (\n                  <Button\n                    variant=\"outline\"\n                    size=\"sm\"\n                    className=\"flex-1\"\n                    onClick={() => window.open(template.preview_url!, '_blank')}\n                  >\n                    <Eye className=\"h-4 w-4 mr-2\" />\n                    Live Preview\n                  </Button>\n                )}\n                <RazorpayButton\n                  amount={template.price}\n                  description={`Purchase ${template.title}`}\n                  onSuccess={(response) => handlePurchaseSuccess(template.id)}\n                  onError={handlePurchaseError}\n                >\n                  <ShoppingCart className=\"h-4 w-4 mr-2\" />\n                  Buy Now\n                </RazorpayButton>\n              </div>\n            </CardContent>\n          </Card>\n        ))}\n      </div>\n\n      {filteredTemplates.length === 0 && !loading && (\n        <div className=\"text-center py-12\">\n          <p className=\"text-muted-foreground\">No templates found matching your criteria.</p>\n        </div>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;;;;;;AAEA;AAAA;AAAA;AAAA;AACA;AAEA;AACA;AAZA;;;;;;;;;;;;AAgBe,SAAS;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IACzD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IACzE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY;QAAC;KAAM;IAE9D,MAAM,WAAW,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;IAE5B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC;QAAW;QAAY;QAAkB;KAAO;IAEpD,MAAM,iBAAiB;QACrB,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,aACL,MAAM,CAAC,KACP,KAAK,CAAC,cAAc;gBAAE,WAAW;YAAM;YAE1C,IAAI,OAAO,MAAM;YAEjB,aAAa,QAAQ,EAAE;YAEvB,4BAA4B;YAC5B,MAAM,mBAAmB;gBAAC;mBAAU,IAAI,IAAI,MAAM,IAAI,CAAA,IAAK,EAAE,QAAQ,KAAK,EAAE;aAAE;YAC9E,cAAc;QAChB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,yBAAyB;QAC7B,IAAI,WAAW;QAEf,wBAAwB;QACxB,IAAI,YAAY;YACd,WAAW,SAAS,MAAM,CAAC,CAAA,WACzB,SAAS,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OAC5D,SAAS,WAAW,EAAE,cAAc,SAAS,WAAW,WAAW;QAEvE;QAEA,qBAAqB;QACrB,IAAI,qBAAqB,OAAO;YAC9B,WAAW,SAAS,MAAM,CAAC,CAAA,WAAY,SAAS,QAAQ,KAAK;QAC/D;QAEA,iBAAiB;QACjB,SAAS,IAAI,CAAC,CAAC,GAAG;YAChB,OAAQ;gBACN,KAAK;oBACH,OAAO,EAAE,KAAK,GAAG,EAAE,KAAK;gBAC1B,KAAK;oBACH,OAAO,EAAE,KAAK,GAAG,EAAE,KAAK;gBAC1B,KAAK;gBACL;oBACE,OAAO,EAAE,KAAK,CAAC,aAAa,CAAC,EAAE,KAAK;YACxC;QACF;QAEA,qBAAqB;IACvB;IAEA,MAAM,wBAAwB,CAAC;QAC7B,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;IACd,mEAAmE;IACrE;IAEA,MAAM,sBAAsB,CAAC;QAC3B,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACZ,QAAQ,KAAK,CAAC,mBAAmB;IACnC;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC;;sCACC,8OAAC;4BAAG,WAAU;sCAAoC;;;;;;sCAClD,8OAAC;4BAAE,WAAU;sCAAwB;;;;;;;;;;;;8BAEvC,8OAAC;oBAAI,WAAU;8BACZ;2BAAI,MAAM;qBAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,8OAAC,gIAAA,CAAA,OAAI;4BAAS,WAAU;;8CACtB,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC,gIAAA,CAAA,aAAU;;sDACT,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;4CAAI,WAAU;;;;;;;;;;;;;2BAJR;;;;;;;;;;;;;;;;IAWrB;IACA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;;kCACC,8OAAC;wBAAG,WAAU;kCAAoC;;;;;;kCAClD,8OAAC;wBAAE,WAAU;kCAAwB;;;;;;;;;;;;0BAMvC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,sMAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;0CAClB,8OAAC,iIAAA,CAAA,QAAK;gCACJ,aAAY;gCACZ,WAAU;gCACV,OAAO;gCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;kCAGjD,8OAAC;wBAAI,WAAU;kCACZ,WAAW,GAAG,CAAC,CAAC,yBACf,8OAAC,kIAAA,CAAA,SAAM;gCAEL,SAAS,aAAa,mBAAmB,YAAY;gCACrD,MAAK;gCACL,SAAS,IAAM,oBAAoB;0CAElC;+BALI;;;;;;;;;;kCASX,8OAAC;wBAAO,OAAO;wBAAQ,eAAe;;0CACpC,8OAAC;gCAAc,WAAU;0CACvB,cAAA,8OAAC;oCAAY,aAAY;;;;;;;;;;;0CAE3B,8OAAC;;kDACC,8OAAC;wCAAW,OAAM;kDAAQ;;;;;;kDAC1B,8OAAC;wCAAW,OAAM;kDAAY;;;;;;kDAC9B,8OAAC;wCAAW,OAAM;kDAAa;;;;;;;;;;;;;;;;;;;;;;;;0BAMrC,8OAAC;gBAAI,WAAU;0BACZ,kBAAkB,GAAG,CAAC,CAAC,yBACtB,8OAAC,gIAAA,CAAA,OAAI;wBAAmB,WAAU;;0CAChC,8OAAC;gCAAI,WAAU;0CACZ,SAAS,aAAa,iBACrB,8OAAC;oCACC,KAAK,SAAS,aAAa;oCAC3B,KAAK,SAAS,KAAK;oCACnB,WAAU;;;;;yDAGZ,8OAAC;oCAAI,WAAU;8CAA0E;;;;;;;;;;;0CAK7F,8OAAC,gIAAA,CAAA,aAAU;0CACT,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC,gIAAA,CAAA,YAAS;oDAAC,WAAU;8DAAW,SAAS,KAAK;;;;;;8DAC9C,8OAAC,gIAAA,CAAA,kBAAe;oDAAC,WAAU;8DACxB,SAAS,WAAW;;;;;;;;;;;;sDAGzB,8OAAC,iIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAa,SAAS,QAAQ;;;;;;;;;;;;;;;;;0CAGjD,8OAAC,gIAAA,CAAA,cAAW;;kDACV,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,oNAAA,CAAA,cAAW;oDAAC,WAAU;;;;;;8DACvB,8OAAC;oDAAK,WAAU;;wDAAqB;wDAAE,SAAS,KAAK;;;;;;;;;;;;;;;;;;kDAIzD,8OAAC;wCAAI,WAAU;;4CACZ,SAAS,WAAW,kBACnB,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,WAAU;gDACV,SAAS,IAAM,OAAO,IAAI,CAAC,SAAS,WAAW,EAAG;;kEAElD,8OAAC,gMAAA,CAAA,MAAG;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;0DAIpC,8OAAC,mJAAA,CAAA,iBAAc;gDACb,QAAQ,SAAS,KAAK;gDACtB,aAAa,CAAC,SAAS,EAAE,SAAS,KAAK,EAAE;gDACzC,WAAW,CAAC,WAAa,sBAAsB,SAAS,EAAE;gDAC1D,SAAS;;kEAET,8OAAC,sNAAA,CAAA,eAAY;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;;;;;;;;uBAnDtC,SAAS,EAAE;;;;;;;;;;YA4DzB,kBAAkB,MAAM,KAAK,KAAK,CAAC,yBAClC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAE,WAAU;8BAAwB;;;;;;;;;;;;;;;;;AAK/C", "debugId": null}}]}