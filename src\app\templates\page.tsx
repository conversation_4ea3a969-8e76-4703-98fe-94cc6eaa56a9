import { <PERSON>, CardContent, Card<PERSON>escription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Search, Eye, Download, Star } from "lucide-react"

const templates = [
  {
    id: 1,
    name: "Modern Business",
    description: "Clean and professional business template with modern design elements",
    category: "Business",
    rating: 4.8,
    downloads: 1234,
    image: "/api/placeholder/300/200",
    tags: ["Professional", "Clean", "Modern"]
  },
  {
    id: 2,
    name: "Creative Portfolio",
    description: "Showcase your creative work with this stunning portfolio template",
    category: "Portfolio",
    rating: 4.9,
    downloads: 987,
    image: "/api/placeholder/300/200",
    tags: ["Creative", "Portfolio", "Artistic"]
  },
  {
    id: 3,
    name: "E-commerce Store",
    description: "Complete e-commerce solution with shopping cart and payment integration",
    category: "E-commerce",
    rating: 4.7,
    downloads: 2156,
    image: "/api/placeholder/300/200",
    tags: ["E-commerce", "Shopping", "Store"]
  },
  {
    id: 4,
    name: "Landing Page Pro",
    description: "High-converting landing page template for marketing campaigns",
    category: "Marketing",
    rating: 4.6,
    downloads: 1567,
    image: "/api/placeholder/300/200",
    tags: ["Landing", "Marketing", "Conversion"]
  },
  {
    id: 5,
    name: "Blog & Magazine",
    description: "Perfect template for blogs, magazines, and content-focused websites",
    category: "Blog",
    rating: 4.5,
    downloads: 892,
    image: "/api/placeholder/300/200",
    tags: ["Blog", "Content", "Magazine"]
  },
  {
    id: 6,
    name: "Restaurant Menu",
    description: "Elegant restaurant template with menu showcase and reservation system",
    category: "Restaurant",
    rating: 4.8,
    downloads: 743,
    image: "/api/placeholder/300/200",
    tags: ["Restaurant", "Food", "Menu"]
  }
]

const categories = ["All", "Business", "Portfolio", "E-commerce", "Marketing", "Blog", "Restaurant"]

export default function TemplatesPage() {
  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Templates</h1>
        <p className="text-muted-foreground">
          Choose from our collection of professionally designed templates
        </p>
      </div>

      {/* Search and Filter */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search templates..."
            className="pl-10"
          />
        </div>
        <div className="flex gap-2 flex-wrap">
          {categories.map((category) => (
            <Button
              key={category}
              variant={category === "All" ? "default" : "outline"}
              size="sm"
            >
              {category}
            </Button>
          ))}
        </div>
      </div>

      {/* Templates Grid */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {templates.map((template) => (
          <Card key={template.id} className="overflow-hidden">
            <div className="aspect-video bg-muted relative">
              <div className="absolute inset-0 flex items-center justify-center text-muted-foreground">
                Template Preview
              </div>
            </div>
            <CardHeader>
              <div className="flex justify-between items-start">
                <div>
                  <CardTitle className="text-lg">{template.name}</CardTitle>
                  <CardDescription className="mt-1">
                    {template.description}
                  </CardDescription>
                </div>
                <Badge variant="secondary">{template.category}</Badge>
              </div>
            </CardHeader>
            <CardContent>
              <div className="flex flex-wrap gap-1 mb-4">
                {template.tags.map((tag) => (
                  <Badge key={tag} variant="outline" className="text-xs">
                    {tag}
                  </Badge>
                ))}
              </div>
              
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center gap-1">
                  <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                  <span className="text-sm font-medium">{template.rating}</span>
                </div>
                <div className="flex items-center gap-1">
                  <Download className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm text-muted-foreground">{template.downloads}</span>
                </div>
              </div>

              <div className="flex gap-2">
                <Button variant="outline" size="sm" className="flex-1">
                  <Eye className="h-4 w-4 mr-2" />
                  Preview
                </Button>
                <Button size="sm" className="flex-1">
                  Use Template
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Load More */}
      <div className="text-center">
        <Button variant="outline">
          Load More Templates
        </Button>
      </div>
    </div>
  )
}
