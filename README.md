# KaleidoneX - Fullstack Next.js Application

A modern fullstack application built with Next.js 15, featuring authentication, payments, and a beautiful UI.

## 🚀 Tech Stack

- **Framework**: Next.js 15 (App Router)
- **Language**: TypeScript
- **Styling**: Tailwind CSS
- **UI Components**: shadcn/ui
- **Authentication & Database**: Supabase
- **Payments**: Razorpay
- **Deployment**: Vercel

## ✨ Features

- 🔐 **Complete Authentication System**: User/Admin roles with secure login/signup
- 💳 **Payment Integration**: Razorpay payment gateway with order tracking
- 📱 **Responsive Design**: Mobile-first with device preview modes
- 🎨 **Advanced Customization**: 50+ template customization options
- 🔒 **Protected Routes**: Role-based access control with middleware
- 📊 **Admin Dashboard**: Comprehensive management panel with analytics
- 🛒 **Order Management**: Complete purchase and order tracking system
- 👤 **User Profiles**: Profile management with role-based features
- 🎯 **Live Preview**: Real-time template customization with responsive preview
- 📧 **Contact System**: Contact form with admin management
- 📈 **Visitor Analytics**: Track and analyze visitor behavior
- 🚀 **Production Ready**: Full-featured application ready for deployment

## 🛠️ Setup Instructions

### 1. Clone the repository

```bash
git clone <your-repo-url>
cd kaleidonex
```

### 2. Install dependencies

```bash
npm install
```

### 3. Environment Variables

Copy the example environment file and fill in your credentials:

```bash
cp .env.example .env.local
```

Fill in the following variables in `.env.local`:

```env
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key

# Razorpay Configuration
NEXT_PUBLIC_RAZORPAY_KEY_ID=your_razorpay_key_id
RAZORPAY_KEY_SECRET=your_razorpay_key_secret

# App Configuration
NEXT_PUBLIC_APP_URL=http://localhost:3000
```

### 4. Supabase Setup

1. Create a new project at [supabase.com](https://supabase.com)
2. Go to Settings > API to get your URL and keys
3. Run the following SQL in the Supabase SQL editor to create the required tables:

```sql
-- Create profiles table with role field
CREATE TABLE profiles (
  id UUID REFERENCES auth.users ON DELETE CASCADE,
  updated_at TIMESTAMP WITH TIME ZONE,
  username TEXT UNIQUE,
  full_name TEXT,
  avatar_url TEXT,
  website TEXT,
  role TEXT DEFAULT 'user',
  PRIMARY KEY (id)
);

-- Create templates table
CREATE TABLE templates (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  title TEXT NOT NULL,
  description TEXT,
  price INTEGER NOT NULL,
  category TEXT NOT NULL,
  preview_image TEXT,
  preview_url TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create orders table
CREATE TABLE orders (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users ON DELETE CASCADE NOT NULL,
  amount INTEGER NOT NULL,
  currency TEXT NOT NULL DEFAULT 'INR',
  status TEXT NOT NULL DEFAULT 'created',
  razorpay_order_id TEXT,
  razorpay_payment_id TEXT,
  razorpay_signature TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create purchases table
CREATE TABLE purchases (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users ON DELETE CASCADE NOT NULL,
  template_id UUID REFERENCES templates ON DELETE CASCADE NOT NULL,
  amount INTEGER NOT NULL,
  currency TEXT NOT NULL DEFAULT 'INR',
  razorpay_payment_id TEXT NOT NULL,
  status TEXT NOT NULL DEFAULT 'completed',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create customizations table
CREATE TABLE customizations (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users ON DELETE CASCADE NOT NULL,
  navbar_style TEXT NOT NULL,
  hero_section TEXT NOT NULL,
  footer_style TEXT NOT NULL,
  config JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create contact_requests table
CREATE TABLE contact_requests (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  name TEXT NOT NULL,
  email TEXT NOT NULL,
  message TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create visitor_logs table
CREATE TABLE visitor_logs (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  ip_address TEXT,
  path TEXT NOT NULL,
  user_agent TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable Row Level Security
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE templates ENABLE ROW LEVEL SECURITY;
ALTER TABLE orders ENABLE ROW LEVEL SECURITY;
ALTER TABLE purchases ENABLE ROW LEVEL SECURITY;
ALTER TABLE customizations ENABLE ROW LEVEL SECURITY;
ALTER TABLE contact_requests ENABLE ROW LEVEL SECURITY;
ALTER TABLE visitor_logs ENABLE ROW LEVEL SECURITY;

-- Create policies for profiles
CREATE POLICY "Users can view own profile" ON profiles FOR SELECT USING (auth.uid() = id);
CREATE POLICY "Users can update own profile" ON profiles FOR UPDATE USING (auth.uid() = id);
CREATE POLICY "Users can insert own profile" ON profiles FOR INSERT WITH CHECK (auth.uid() = id);

-- Create policies for templates (public read, admin write)
CREATE POLICY "Anyone can view templates" ON templates FOR SELECT USING (true);
CREATE POLICY "Only admins can manage templates" ON templates FOR ALL USING (
  EXISTS (
    SELECT 1 FROM profiles
    WHERE profiles.id = auth.uid()
    AND profiles.role = 'admin'
  )
);

-- Create policies for orders
CREATE POLICY "Users can view own orders" ON orders FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can create own orders" ON orders FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update own orders" ON orders FOR UPDATE USING (auth.uid() = user_id);

-- Create policies for purchases
CREATE POLICY "Users can view own purchases" ON purchases FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can create own purchases" ON purchases FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Create policies for customizations
CREATE POLICY "Users can view own customizations" ON customizations FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can create own customizations" ON customizations FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update own customizations" ON customizations FOR UPDATE USING (auth.uid() = user_id);

-- Create policies for contact_requests (admin only)
CREATE POLICY "Only admins can view contact requests" ON contact_requests FOR SELECT USING (
  EXISTS (
    SELECT 1 FROM profiles
    WHERE profiles.id = auth.uid()
    AND profiles.role = 'admin'
  )
);
CREATE POLICY "Anyone can create contact requests" ON contact_requests FOR INSERT WITH CHECK (true);

-- Create policies for visitor_logs (admin only)
CREATE POLICY "Only admins can view visitor logs" ON visitor_logs FOR SELECT USING (
  EXISTS (
    SELECT 1 FROM profiles
    WHERE profiles.id = auth.uid()
    AND profiles.role = 'admin'
  )
);
CREATE POLICY "Anyone can create visitor logs" ON visitor_logs FOR INSERT WITH CHECK (true);

-- Insert sample templates
INSERT INTO templates (title, description, price, category, preview_image, preview_url) VALUES
('Modern Business', 'Clean and professional business template with modern design elements', 2999, 'Business', null, null),
('Creative Portfolio', 'Showcase your creative work with this stunning portfolio template', 1999, 'Portfolio', null, null),
('E-commerce Store', 'Complete e-commerce solution with shopping cart and payment integration', 4999, 'E-commerce', null, null),
('Landing Page Pro', 'High-converting landing page template for marketing campaigns', 1499, 'Marketing', null, null),
('Blog & Magazine', 'Perfect template for blogs, magazines, and content-focused websites', 999, 'Blog', null, null),
('Restaurant Menu', 'Elegant restaurant template with menu showcase and reservation system', 2499, 'Restaurant', null, null);

-- Create demo users (Optional - for testing)
-- You can create these manually or use the demo login buttons in the app

-- Admin user (you'll need to sign up with these credentials first)
-- Email: <EMAIL>
-- Password: admin123
-- Then run this SQL to make them admin:
-- UPDATE profiles SET role = 'admin' WHERE id = (SELECT id FROM auth.users WHERE email = '<EMAIL>');

-- Regular user (you'll need to sign up with these credentials first)
-- Email: <EMAIL>
-- Password: user123
-- Role will be 'user' by default
```

### 5. Razorpay Setup

1. Create an account at [razorpay.com](https://razorpay.com)
2. Go to Settings > API Keys to get your Key ID and Secret
3. Add the credentials to your `.env.local` file

### 6. Run the development server

```bash
npm run dev
```

Open [http://localhost:3000](http://localhost:3000) to see the application.

## 🔐 Authentication System

### User Roles

The application supports two user roles:

- **User**: Regular users who can browse templates, make purchases, and customize templates
- **Admin**: Administrators who have access to the admin panel and can manage all data

### Authentication Features

- **Sign Up/Sign In**: Email and password authentication
- **Google OAuth**: Sign in with Google account
- **Password Reset**: Forgot password functionality with email reset
- **Role-based Access**: Different features based on user role
- **Protected Routes**: Automatic redirection based on authentication status
- **Session Management**: Persistent login sessions

### Demo Accounts

For testing purposes, you can use these demo accounts:

#### Admin Account
- **Email**: <EMAIL>
- **Password**: admin123
- **Access**: Full admin panel access

#### User Account
- **Email**: <EMAIL>
- **Password**: user123
- **Access**: Standard user features

### Quick Demo Access

The login page includes "Demo Login" buttons that automatically fill in the credentials for quick testing.

### Creating Admin Users

To make a user an admin:

1. Sign up with a regular account
2. Go to your Supabase dashboard
3. Navigate to Table Editor > profiles
4. Find the user and change their `role` from 'user' to 'admin'

Or run this SQL in Supabase:

```sql
UPDATE profiles
SET role = 'admin'
WHERE id = (SELECT id FROM auth.users WHERE email = '<EMAIL>');
```

## 📁 Project Structure

```
src/
├── app/                    # Next.js App Router
│   ├── (dashboard)/       # Dashboard routes
│   ├── api/               # API routes
│   ├── globals.css        # Global styles
│   ├── layout.tsx         # Root layout
│   └── page.tsx           # Home page
├── components/            # React components
│   ├── layout/           # Layout components
│   ├── navigation/       # Navigation components
│   └── ui/               # shadcn/ui components
├── hooks/                # Custom React hooks
├── lib/                  # Utility libraries
│   ├── supabase/        # Supabase configuration
│   ├── database.types.ts # Database types
│   ├── razorpay.ts      # Razorpay configuration
│   └── utils.ts         # Utility functions
└── middleware.ts         # Next.js middleware
```

## 🚀 Deployment

### Deploy to Vercel

1. Push your code to GitHub
2. Connect your repository to [Vercel](https://vercel.com)
3. Add your environment variables in the Vercel dashboard
4. Deploy!

The application is optimized for Vercel deployment with automatic builds and deployments.

## 🔧 Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint

## 📝 License

This project is licensed under the MIT License.
