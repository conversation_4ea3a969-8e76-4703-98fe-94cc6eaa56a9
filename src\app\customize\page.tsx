"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { createClient } from "@/lib/supabase/client"
import { getUser } from "@/lib/auth"
import { toast } from "sonner"
import {
  Save,
  Eye,
  Mail
} from "lucide-react"

const navbarStyles = [
  { id: "modern", name: "Modern", description: "Clean and minimal navigation" },
  { id: "classic", name: "Classic", description: "Traditional horizontal menu" },
  { id: "sidebar", name: "Sidebar", description: "Vertical side navigation" },
  { id: "floating", name: "Floating", description: "Floating navigation bar" }
]

const heroSections = [
  { id: "hero1", name: "Hero with Image", description: "Large hero with background image" },
  { id: "hero2", name: "Split Hero", description: "Text on left, image on right" },
  { id: "hero3", name: "Centered Hero", description: "Centered text with call-to-action" },
  { id: "hero4", name: "Video Hero", description: "Hero with background video" }
]

const footerStyles = [
  { id: "simple", name: "Simple", description: "Minimal footer with links" },
  { id: "detailed", name: "Detailed", description: "Multi-column footer" },
  { id: "newsletter", name: "Newsletter", description: "Footer with newsletter signup" },
  { id: "social", name: "Social", description: "Footer focused on social links" }
]

export default function CustomizePage() {
  const [navbarStyle, setNavbarStyle] = useState("modern")
  const [heroSection, setHeroSection] = useState("hero1")
  const [footerStyle, setFooterStyle] = useState("simple")
  const [user, setUser] = useState<any>(null)
  const [saving, setSaving] = useState(false)

  const supabase = createClient()

  useEffect(() => {
    checkUser()
  }, [])

  const checkUser = async () => {
    const { data: { user } } = await supabase.auth.getUser()
    setUser(user)
  }

  const saveCustomization = async () => {
    if (!user) {
      toast.error('Please login to save customizations')
      return
    }

    setSaving(true)
    try {
      const config = {
        navbarStyle,
        heroSection,
        footerStyle,
        timestamp: new Date().toISOString()
      }

      const { error } = await supabase
        .from('customizations')
        .insert({
          user_id: user.id,
          navbar_style: navbarStyle,
          hero_section: heroSection,
          footer_style: footerStyle,
          config
        })

      if (error) throw error

      toast.success('Customization saved successfully!')
    } catch (error) {
      console.error('Error saving customization:', error)
      toast.error('Failed to save customization')
    } finally {
      setSaving(false)
    }
  }

  const contactToBuy = async () => {
    if (!user) {
      toast.error('Please login to contact us')
      return
    }

    try {
      const config = {
        navbarStyle,
        heroSection,
        footerStyle,
        timestamp: new Date().toISOString()
      }

      // Save the customization first
      const { error: saveError } = await supabase
        .from('customizations')
        .insert({
          user_id: user.id,
          navbar_style: navbarStyle,
          hero_section: heroSection,
          footer_style: footerStyle,
          config
        })

      if (saveError) throw saveError

      // Create a contact request
      const { error: contactError } = await supabase
        .from('contact_requests')
        .insert({
          name: user.user_metadata?.full_name || user.email,
          email: user.email,
          message: `I'm interested in purchasing a custom template with the following configuration:

Navbar Style: ${navbarStyles.find(n => n.id === navbarStyle)?.name}
Hero Section: ${heroSections.find(h => h.id === heroSection)?.name}
Footer Style: ${footerStyles.find(f => f.id === footerStyle)?.name}

Please contact me with pricing and timeline information.`
        })

      if (contactError) throw contactError

      toast.success('Your customization has been saved and we will contact you soon!')
    } catch (error) {
      console.error('Error:', error)
      toast.error('Failed to process request')
    }
  }

  const getNavbarStyles = (style: string) => {
    switch (style) {
      case "modern":
        return "bg-white shadow-sm"
      case "classic":
        return "bg-gray-100"
      case "sidebar":
        return "bg-gray-900 text-white"
      case "floating":
        return "bg-white shadow-lg rounded-lg mx-4 mt-4"
      default:
        return "bg-white"
    }
  }

  const getHeroStyles = (style: string) => {
    switch (style) {
      case "hero1":
        return "bg-gradient-to-r from-blue-500 to-purple-600 text-white text-center"
      case "hero2":
        return "bg-gray-50"
      case "hero3":
        return "bg-white text-center"
      case "hero4":
        return "bg-black text-white text-center"
      default:
        return "bg-gray-50"
    }
  }

  const getFooterStyles = (style: string) => {
    switch (style) {
      case "simple":
        return "bg-gray-100 text-center"
      case "detailed":
        return "bg-gray-900 text-white"
      case "newsletter":
        return "bg-blue-50"
      case "social":
        return "bg-gray-800 text-white text-center"
      default:
        return "bg-gray-100"
    }
  }

  const renderHeroContent = (style: string) => {
    switch (style) {
      case "hero1":
        return (
          <div>
            <h1 className="text-4xl font-bold mb-4">Welcome to Your Website</h1>
            <p className="text-xl mb-6">Create amazing experiences with our templates</p>
            <Button className="bg-white text-blue-600 hover:bg-gray-100">Get Started</Button>
          </div>
        )
      case "hero2":
        return (
          <div className="grid md:grid-cols-2 gap-8 items-center">
            <div>
              <h1 className="text-4xl font-bold mb-4">Split Hero Section</h1>
              <p className="text-lg mb-6">Text content on the left side with image on the right</p>
              <Button>Learn More</Button>
            </div>
            <div className="bg-gray-200 h-64 rounded-lg flex items-center justify-center">
              <span className="text-gray-500">Hero Image</span>
            </div>
          </div>
        )
      case "hero3":
        return (
          <div>
            <h1 className="text-4xl font-bold mb-4">Centered Hero</h1>
            <p className="text-lg mb-6">Perfect for landing pages and focused messaging</p>
            <div className="flex gap-4 justify-center">
              <Button>Primary Action</Button>
              <Button variant="outline">Secondary Action</Button>
            </div>
          </div>
        )
      case "hero4":
        return (
          <div>
            <h1 className="text-4xl font-bold mb-4">Video Hero Background</h1>
            <p className="text-xl mb-6">Engaging video background for maximum impact</p>
            <Button className="bg-white text-black hover:bg-gray-100">Watch Demo</Button>
          </div>
        )
      default:
        return <div>Hero Content</div>
    }
  }

  const renderFooterContent = (style: string) => {
    switch (style) {
      case "simple":
        return (
          <div>
            <p>&copy; 2024 Your Company. All rights reserved.</p>
          </div>
        )
      case "detailed":
        return (
          <div className="grid md:grid-cols-4 gap-6">
            <div>
              <h4 className="font-semibold mb-2">Company</h4>
              <ul className="space-y-1 text-sm">
                <li>About</li>
                <li>Careers</li>
                <li>Contact</li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold mb-2">Products</h4>
              <ul className="space-y-1 text-sm">
                <li>Templates</li>
                <li>Tools</li>
                <li>Support</li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold mb-2">Resources</h4>
              <ul className="space-y-1 text-sm">
                <li>Blog</li>
                <li>Documentation</li>
                <li>Help Center</li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold mb-2">Legal</h4>
              <ul className="space-y-1 text-sm">
                <li>Privacy</li>
                <li>Terms</li>
                <li>Cookies</li>
              </ul>
            </div>
          </div>
        )
      case "newsletter":
        return (
          <div className="text-center">
            <h4 className="font-semibold mb-2">Subscribe to our newsletter</h4>
            <p className="text-sm mb-4">Get the latest updates and offers</p>
            <div className="flex gap-2 justify-center max-w-md mx-auto">
              <input
                type="email"
                placeholder="Enter your email"
                className="flex-1 px-3 py-2 border rounded"
              />
              <Button>Subscribe</Button>
            </div>
          </div>
        )
      case "social":
        return (
          <div>
            <div className="flex justify-center gap-4 mb-4">
              <span>📘</span>
              <span>🐦</span>
              <span>📷</span>
              <span>💼</span>
            </div>
            <p>&copy; 2024 Your Company. Follow us on social media.</p>
          </div>
        )
      default:
        return <div>Footer Content</div>
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Live Preview Customizer</h1>
          <p className="text-muted-foreground">
            Customize your template in real-time with our visual editor
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" size="sm" onClick={saveCustomization} disabled={saving || !user}>
            <Save className="h-4 w-4 mr-2" />
            {saving ? 'Saving...' : 'Save'}
          </Button>
          <Button size="sm" onClick={contactToBuy} disabled={!user}>
            <Mail className="h-4 w-4 mr-2" />
            Contact to Buy
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Customization Panel */}
        <div className="lg:col-span-1">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Customization Options</CardTitle>
              <CardDescription>
                Choose your template components
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Navbar Style */}
              <div className="space-y-2">
                <Label>Navbar Style</Label>
                <Select value={navbarStyle} onValueChange={setNavbarStyle}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select navbar style" />
                  </SelectTrigger>
                  <SelectContent>
                    {navbarStyles.map((style) => (
                      <SelectItem key={style.id} value={style.id}>
                        <div>
                          <div className="font-medium">{style.name}</div>
                          <div className="text-sm text-muted-foreground">{style.description}</div>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Hero Section */}
              <div className="space-y-2">
                <Label>Hero Section</Label>
                <Select value={heroSection} onValueChange={setHeroSection}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select hero section" />
                  </SelectTrigger>
                  <SelectContent>
                    {heroSections.map((hero) => (
                      <SelectItem key={hero.id} value={hero.id}>
                        <div>
                          <div className="font-medium">{hero.name}</div>
                          <div className="text-sm text-muted-foreground">{hero.description}</div>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Footer Style */}
              <div className="space-y-2">
                <Label>Footer Style</Label>
                <Select value={footerStyle} onValueChange={setFooterStyle}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select footer style" />
                  </SelectTrigger>
                  <SelectContent>
                    {footerStyles.map((footer) => (
                      <SelectItem key={footer.id} value={footer.id}>
                        <div>
                          <div className="font-medium">{footer.name}</div>
                          <div className="text-sm text-muted-foreground">{footer.description}</div>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {!user && (
                <div className="p-4 bg-muted rounded-lg">
                  <p className="text-sm text-muted-foreground">
                    Please login to save your customizations
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Live Preview */}
        <div className="lg:col-span-3">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Live Preview</CardTitle>
              <CardDescription>
                See your template preview based on your selections
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="min-h-[600px] border-2 border-dashed border-muted-foreground/25 rounded-lg overflow-hidden">
                {/* Navbar Preview */}
                <div className={`p-4 border-b ${getNavbarStyles(navbarStyle)}`}>
                  <div className="flex justify-between items-center">
                    <div className="font-bold text-lg">Your Brand</div>
                    <div className="flex gap-4">
                      <span>Home</span>
                      <span>About</span>
                      <span>Services</span>
                      <span>Contact</span>
                    </div>
                  </div>
                </div>

                {/* Hero Section Preview */}
                <div className={`p-8 ${getHeroStyles(heroSection)}`}>
                  {renderHeroContent(heroSection)}
                </div>

                {/* Content Section */}
                <div className="p-8">
                  <div className="grid md:grid-cols-3 gap-6">
                    {[1, 2, 3].map((i) => (
                      <div key={i} className="text-center">
                        <div className="w-16 h-16 bg-primary/10 rounded-full mx-auto mb-4 flex items-center justify-center">
                          <span className="text-2xl">🎨</span>
                        </div>
                        <h3 className="font-semibold mb-2">Feature {i}</h3>
                        <p className="text-sm text-muted-foreground">
                          Description of feature {i} goes here.
                        </p>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Footer Preview */}
                <div className={`p-6 border-t mt-8 ${getFooterStyles(footerStyle)}`}>
                  {renderFooterContent(footerStyle)}
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Export Options */}
      <Card>
        <CardHeader>
          <CardTitle>Export Options</CardTitle>
          <CardDescription>
            Download your customized template
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex gap-4">
            <Button>
              <Download className="h-4 w-4 mr-2" />
              Download HTML/CSS
            </Button>
            <Button variant="outline">
              <Download className="h-4 w-4 mr-2" />
              Download React Components
            </Button>
            <Button variant="outline">
              <Download className="h-4 w-4 mr-2" />
              Export as PDF
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
