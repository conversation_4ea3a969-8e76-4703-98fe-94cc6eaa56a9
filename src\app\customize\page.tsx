"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Slider } from "@/components/ui/slider"
import { Badge } from "@/components/ui/badge"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { createClient } from "@/lib/supabase/client"
import { getUser } from "@/lib/auth"
import { toast } from "sonner"
import {
  Save,
  Eye,
  Mail,
  Palette,
  Type,
  Layout,
  Settings,
  Globe,
  Smartphone,
  Monitor,
  Tablet,
  Moon,
  Sun,
  Zap,
  Image,
  Code,
  Search,
  Facebook,
  Twitter,
  Instagram,
  Linkedin,
  Youtube,
  Phone,
  MapPin,
  Download,
  Upload,
  Trash2,
  <PERSON><PERSON>,
  <PERSON>fresh<PERSON><PERSON>
} from "lucide-react"

// Enhanced Configuration Options
const navbarStyles = [
  { id: "modern", name: "Modern", description: "Clean and minimal navigation" },
  { id: "classic", name: "Classic", description: "Traditional horizontal menu" },
  { id: "sidebar", name: "Sidebar", description: "Vertical side navigation" },
  { id: "floating", name: "Floating", description: "Floating navigation bar" },
  { id: "sticky", name: "Sticky", description: "Fixed navigation on scroll" },
  { id: "transparent", name: "Transparent", description: "Transparent overlay navigation" },
  { id: "mega", name: "Mega Menu", description: "Large dropdown navigation" },
  { id: "hamburger", name: "Hamburger", description: "Mobile-first hamburger menu" }
]

const heroSections = [
  { id: "hero1", name: "Hero with Image", description: "Large hero with background image" },
  { id: "hero2", name: "Split Hero", description: "Text on left, image on right" },
  { id: "hero3", name: "Centered Hero", description: "Centered text with call-to-action" },
  { id: "hero4", name: "Video Hero", description: "Hero with background video" },
  { id: "hero5", name: "Carousel Hero", description: "Multiple slides with transitions" },
  { id: "hero6", name: "Parallax Hero", description: "Parallax scrolling effect" },
  { id: "hero7", name: "Animated Hero", description: "Text animations and effects" },
  { id: "hero8", name: "Form Hero", description: "Hero with integrated form" }
]

const footerStyles = [
  { id: "simple", name: "Simple", description: "Minimal footer with links" },
  { id: "detailed", name: "Detailed", description: "Multi-column footer" },
  { id: "newsletter", name: "Newsletter", description: "Footer with newsletter signup" },
  { id: "social", name: "Social", description: "Footer focused on social links" },
  { id: "contact", name: "Contact", description: "Footer with contact information" },
  { id: "sitemap", name: "Sitemap", description: "Comprehensive site navigation" },
  { id: "minimal", name: "Minimal", description: "Ultra-clean minimal footer" },
  { id: "corporate", name: "Corporate", description: "Professional business footer" }
]

const layoutStyles = [
  { id: "boxed", name: "Boxed", description: "Contained layout with margins" },
  { id: "fullwidth", name: "Full Width", description: "Edge-to-edge layout" },
  { id: "fluid", name: "Fluid", description: "Responsive fluid layout" },
  { id: "grid", name: "Grid", description: "CSS Grid based layout" },
  { id: "flexbox", name: "Flexbox", description: "Flexible box layout" }
]

const colorSchemes = [
  { id: "blue", name: "Ocean Blue", primary: "#3B82F6", secondary: "#1E40AF", accent: "#60A5FA" },
  { id: "purple", name: "Royal Purple", primary: "#8B5CF6", secondary: "#7C3AED", accent: "#A78BFA" },
  { id: "green", name: "Nature Green", primary: "#10B981", secondary: "#059669", accent: "#34D399" },
  { id: "red", name: "Passion Red", primary: "#EF4444", secondary: "#DC2626", accent: "#F87171" },
  { id: "orange", name: "Sunset Orange", primary: "#F97316", secondary: "#EA580C", accent: "#FB923C" },
  { id: "pink", name: "Rose Pink", primary: "#EC4899", secondary: "#DB2777", accent: "#F472B6" },
  { id: "indigo", name: "Deep Indigo", primary: "#6366F1", secondary: "#4F46E5", accent: "#818CF8" },
  { id: "teal", name: "Ocean Teal", primary: "#14B8A6", secondary: "#0D9488", accent: "#5EEAD4" }
]

const fontFamilies = [
  { id: "inter", name: "Inter", description: "Modern sans-serif" },
  { id: "roboto", name: "Roboto", description: "Google's signature font" },
  { id: "opensans", name: "Open Sans", description: "Friendly and readable" },
  { id: "lato", name: "Lato", description: "Humanist sans-serif" },
  { id: "montserrat", name: "Montserrat", description: "Geometric sans-serif" },
  { id: "playfair", name: "Playfair Display", description: "Elegant serif" },
  { id: "merriweather", name: "Merriweather", description: "Reading-focused serif" },
  { id: "poppins", name: "Poppins", description: "Rounded geometric" }
]

const animationTypes = [
  { id: "none", name: "None", description: "No animations" },
  { id: "fade", name: "Fade In", description: "Smooth fade transitions" },
  { id: "slide", name: "Slide Up", description: "Elements slide into view" },
  { id: "zoom", name: "Zoom In", description: "Scale animations" },
  { id: "bounce", name: "Bounce", description: "Playful bounce effects" },
  { id: "rotate", name: "Rotate", description: "Rotation animations" },
  { id: "flip", name: "Flip", description: "3D flip effects" },
  { id: "typewriter", name: "Typewriter", description: "Text typing effect" }
]

export default function CustomizePage() {
  // Layout & Structure
  const [navbarStyle, setNavbarStyle] = useState("modern")
  const [heroSection, setHeroSection] = useState("hero1")
  const [footerStyle, setFooterStyle] = useState("simple")
  const [layoutStyle, setLayoutStyle] = useState("fullwidth")

  // Colors & Theme
  const [colorScheme, setColorScheme] = useState("blue")
  const [primaryColor, setPrimaryColor] = useState("#3B82F6")
  const [secondaryColor, setSecondaryColor] = useState("#1E40AF")
  const [accentColor, setAccentColor] = useState("#60A5FA")
  const [backgroundColor, setBackgroundColor] = useState("#FFFFFF")
  const [textColor, setTextColor] = useState("#1F2937")
  const [darkMode, setDarkMode] = useState(false)

  // Typography
  const [fontFamily, setFontFamily] = useState("inter")
  const [fontSize, setFontSize] = useState([16])
  const [lineHeight, setLineHeight] = useState([1.6])
  const [fontWeight, setFontWeight] = useState("400")

  // Content
  const [siteName, setSiteName] = useState("Your Brand")
  const [tagline, setTagline] = useState("Create amazing experiences")
  const [description, setDescription] = useState("Professional templates for modern websites")
  const [logoUrl, setLogoUrl] = useState("")

  // Contact Information
  const [email, setEmail] = useState("")
  const [phone, setPhone] = useState("")
  const [address, setAddress] = useState("")

  // Social Media
  const [facebook, setFacebook] = useState("")
  const [twitter, setTwitter] = useState("")
  const [instagram, setInstagram] = useState("")
  const [linkedin, setLinkedin] = useState("")
  const [youtube, setYoutube] = useState("")

  // Features
  const [animations, setAnimations] = useState("fade")
  const [rtl, setRtl] = useState(false)
  const [multiLanguage, setMultiLanguage] = useState(false)

  // SEO
  const [metaTitle, setMetaTitle] = useState("")
  const [metaDescription, setMetaDescription] = useState("")
  const [keywords, setKeywords] = useState("")

  // Advanced
  const [customCSS, setCustomCSS] = useState("")
  const [customJS, setCustomJS] = useState("")
  const [googleAnalytics, setGoogleAnalytics] = useState("")

  // Responsive
  const [mobileLayout, setMobileLayout] = useState("mobile")
  const [tabletLayout, setTabletLayout] = useState("tablet")
  const [desktopLayout, setDesktopLayout] = useState("desktop")

  // Component state
  const [user, setUser] = useState<any>(null)
  const [saving, setSaving] = useState(false)
  const [activeTab, setActiveTab] = useState("layout")
  const [previewDevice, setPreviewDevice] = useState("desktop")

  const supabase = createClient()

  useEffect(() => {
    checkUser()
  }, [])

  const checkUser = async () => {
    const { data: { user } } = await supabase.auth.getUser()
    setUser(user)
  }

  const saveCustomization = async () => {
    if (!user) {
      toast.error('Please login to save customizations')
      return
    }

    setSaving(true)
    try {
      const config = {
        // Layout & Structure
        navbarStyle,
        heroSection,
        footerStyle,
        layoutStyle,

        // Colors & Theme
        colorScheme,
        primaryColor,
        secondaryColor,
        accentColor,
        backgroundColor,
        textColor,
        darkMode,

        // Typography
        fontFamily,
        fontSize: fontSize[0],
        lineHeight: lineHeight[0],
        fontWeight,

        // Content
        siteName,
        tagline,
        description,
        logoUrl,

        // Contact Information
        email,
        phone,
        address,

        // Social Media
        facebook,
        twitter,
        instagram,
        linkedin,
        youtube,

        // Features
        animations,
        rtl,
        multiLanguage,

        // SEO
        metaTitle,
        metaDescription,
        keywords,

        // Advanced
        customCSS,
        customJS,
        googleAnalytics,

        // Responsive
        mobileLayout,
        tabletLayout,
        desktopLayout,

        timestamp: new Date().toISOString()
      }

      const { error } = await supabase
        .from('customizations')
        .insert({
          user_id: user.id,
          navbar_style: navbarStyle,
          hero_section: heroSection,
          footer_style: footerStyle,
          config
        })

      if (error) throw error

      toast.success('Customization saved successfully!')
    } catch (error) {
      console.error('Error saving customization:', error)
      toast.error('Failed to save customization')
    } finally {
      setSaving(false)
    }
  }

  const contactToBuy = async () => {
    if (!user) {
      toast.error('Please login to contact us')
      return
    }

    try {
      const config = {
        navbarStyle,
        heroSection,
        footerStyle,
        timestamp: new Date().toISOString()
      }

      // Save the customization first
      const { error: saveError } = await supabase
        .from('customizations')
        .insert({
          user_id: user.id,
          navbar_style: navbarStyle,
          hero_section: heroSection,
          footer_style: footerStyle,
          config
        })

      if (saveError) throw saveError

      // Create a contact request
      const { error: contactError } = await supabase
        .from('contact_requests')
        .insert({
          name: user.user_metadata?.full_name || user.email,
          email: user.email,
          message: `I'm interested in purchasing a custom template with the following configuration:

Navbar Style: ${navbarStyles.find(n => n.id === navbarStyle)?.name}
Hero Section: ${heroSections.find(h => h.id === heroSection)?.name}
Footer Style: ${footerStyles.find(f => f.id === footerStyle)?.name}

Please contact me with pricing and timeline information.`
        })

      if (contactError) throw contactError

      toast.success('Your customization has been saved and we will contact you soon!')
    } catch (error) {
      console.error('Error:', error)
      toast.error('Failed to process request')
    }
  }

  const getNavbarStyles = (style: string) => {
    switch (style) {
      case "modern":
        return "bg-white shadow-sm"
      case "classic":
        return "bg-gray-100"
      case "sidebar":
        return "bg-gray-900 text-white"
      case "floating":
        return "bg-white shadow-lg rounded-lg mx-4 mt-4"
      default:
        return "bg-white"
    }
  }

  const getHeroStyles = (style: string) => {
    switch (style) {
      case "hero1":
        return "bg-gradient-to-r from-blue-500 to-purple-600 text-white text-center"
      case "hero2":
        return "bg-gray-50"
      case "hero3":
        return "bg-white text-center"
      case "hero4":
        return "bg-black text-white text-center"
      default:
        return "bg-gray-50"
    }
  }

  const getFooterStyles = (style: string) => {
    switch (style) {
      case "simple":
        return "bg-gray-100 text-center"
      case "detailed":
        return "bg-gray-900 text-white"
      case "newsletter":
        return "bg-blue-50"
      case "social":
        return "bg-gray-800 text-white text-center"
      default:
        return "bg-gray-100"
    }
  }

  const renderHeroContent = (style: string) => {
    switch (style) {
      case "hero1":
        return (
          <div>
            <h1 className="text-4xl font-bold mb-4">Welcome to Your Website</h1>
            <p className="text-xl mb-6">Create amazing experiences with our templates</p>
            <Button className="bg-white text-blue-600 hover:bg-gray-100">Get Started</Button>
          </div>
        )
      case "hero2":
        return (
          <div className="grid md:grid-cols-2 gap-8 items-center">
            <div>
              <h1 className="text-4xl font-bold mb-4">Split Hero Section</h1>
              <p className="text-lg mb-6">Text content on the left side with image on the right</p>
              <Button>Learn More</Button>
            </div>
            <div className="bg-gray-200 h-64 rounded-lg flex items-center justify-center">
              <span className="text-gray-500">Hero Image</span>
            </div>
          </div>
        )
      case "hero3":
        return (
          <div>
            <h1 className="text-4xl font-bold mb-4">Centered Hero</h1>
            <p className="text-lg mb-6">Perfect for landing pages and focused messaging</p>
            <div className="flex gap-4 justify-center">
              <Button>Primary Action</Button>
              <Button variant="outline">Secondary Action</Button>
            </div>
          </div>
        )
      case "hero4":
        return (
          <div>
            <h1 className="text-4xl font-bold mb-4">Video Hero Background</h1>
            <p className="text-xl mb-6">Engaging video background for maximum impact</p>
            <Button className="bg-white text-black hover:bg-gray-100">Watch Demo</Button>
          </div>
        )
      default:
        return <div>Hero Content</div>
    }
  }

  const renderFooterContent = (style: string) => {
    switch (style) {
      case "simple":
        return (
          <div>
            <p>&copy; 2024 Your Company. All rights reserved.</p>
          </div>
        )
      case "detailed":
        return (
          <div className="grid md:grid-cols-4 gap-6">
            <div>
              <h4 className="font-semibold mb-2">Company</h4>
              <ul className="space-y-1 text-sm">
                <li>About</li>
                <li>Careers</li>
                <li>Contact</li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold mb-2">Products</h4>
              <ul className="space-y-1 text-sm">
                <li>Templates</li>
                <li>Tools</li>
                <li>Support</li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold mb-2">Resources</h4>
              <ul className="space-y-1 text-sm">
                <li>Blog</li>
                <li>Documentation</li>
                <li>Help Center</li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold mb-2">Legal</h4>
              <ul className="space-y-1 text-sm">
                <li>Privacy</li>
                <li>Terms</li>
                <li>Cookies</li>
              </ul>
            </div>
          </div>
        )
      case "newsletter":
        return (
          <div className="text-center">
            <h4 className="font-semibold mb-2">Subscribe to our newsletter</h4>
            <p className="text-sm mb-4">Get the latest updates and offers</p>
            <div className="flex gap-2 justify-center max-w-md mx-auto">
              <input
                type="email"
                placeholder="Enter your email"
                className="flex-1 px-3 py-2 border rounded"
              />
              <Button>Subscribe</Button>
            </div>
          </div>
        )
      case "social":
        return (
          <div>
            <div className="flex justify-center gap-4 mb-4">
              <span>📘</span>
              <span>🐦</span>
              <span>📷</span>
              <span>💼</span>
            </div>
            <p>&copy; 2024 Your Company. Follow us on social media.</p>
          </div>
        )
      default:
        return <div>Footer Content</div>
    }
  }

  // Helper functions for color scheme changes
  const applyColorScheme = (scheme: any) => {
    setPrimaryColor(scheme.primary)
    setSecondaryColor(scheme.secondary)
    setAccentColor(scheme.accent)
  }

  const resetToDefaults = () => {
    setNavbarStyle("modern")
    setHeroSection("hero1")
    setFooterStyle("simple")
    setLayoutStyle("fullwidth")
    setColorScheme("blue")
    setPrimaryColor("#3B82F6")
    setSecondaryColor("#1E40AF")
    setAccentColor("#60A5FA")
    setBackgroundColor("#FFFFFF")
    setTextColor("#1F2937")
    setDarkMode(false)
    setFontFamily("inter")
    setFontSize([16])
    setLineHeight([1.6])
    setFontWeight("400")
    setSiteName("Your Brand")
    setTagline("Create amazing experiences")
    setDescription("Professional templates for modern websites")
    setAnimations("fade")
    setRtl(false)
    setMultiLanguage(false)
    toast.success('Reset to default settings')
  }

  const exportConfig = () => {
    const config = {
      navbarStyle, heroSection, footerStyle, layoutStyle,
      colorScheme, primaryColor, secondaryColor, accentColor,
      backgroundColor, textColor, darkMode, fontFamily,
      fontSize: fontSize[0], lineHeight: lineHeight[0], fontWeight,
      siteName, tagline, description, logoUrl, email, phone, address,
      facebook, twitter, instagram, linkedin, youtube,
      animations, rtl, multiLanguage, metaTitle, metaDescription, keywords,
      customCSS, customJS, googleAnalytics,
      mobileLayout, tabletLayout, desktopLayout
    }

    const blob = new Blob([JSON.stringify(config, null, 2)], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `template-config-${new Date().toISOString().split('T')[0]}.json`
    a.click()
    URL.revokeObjectURL(url)
    toast.success('Configuration exported!')
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Advanced Template Customizer</h1>
          <p className="text-muted-foreground">
            Create your perfect website with our comprehensive customization tools
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" size="sm" onClick={resetToDefaults}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Reset
          </Button>
          <Button variant="outline" size="sm" onClick={exportConfig}>
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
          <Button variant="outline" size="sm" onClick={saveCustomization} disabled={saving || !user}>
            <Save className="h-4 w-4 mr-2" />
            {saving ? 'Saving...' : 'Save'}
          </Button>
          <Button size="sm" onClick={contactToBuy} disabled={!user}>
            <Mail className="h-4 w-4 mr-2" />
            Contact to Buy
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Enhanced Customization Panel */}
        <div className="lg:col-span-1">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Customization Studio</CardTitle>
              <CardDescription>
                Professional-grade customization tools
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
                <TabsList className="grid w-full grid-cols-2">
                  <TabsTrigger value="layout" className="text-xs">
                    <Layout className="h-3 w-3 mr-1" />
                    Layout
                  </TabsTrigger>
                  <TabsTrigger value="design" className="text-xs">
                    <Palette className="h-3 w-3 mr-1" />
                    Design
                  </TabsTrigger>
                </TabsList>

                <div className="mt-4 space-y-4">
                  <TabsContent value="layout" className="space-y-4 mt-0">
                    {/* Layout & Structure */}
                    <div className="space-y-3">
                      <Label className="text-sm font-semibold">Layout Structure</Label>

                      <div className="space-y-2">
                        <Label className="text-xs">Layout Style</Label>
                        <Select value={layoutStyle} onValueChange={setLayoutStyle}>
                          <SelectTrigger className="h-8">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            {layoutStyles.map((layout) => (
                              <SelectItem key={layout.id} value={layout.id}>
                                <div>
                                  <div className="font-medium text-xs">{layout.name}</div>
                                  <div className="text-xs text-muted-foreground">{layout.description}</div>
                                </div>
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>

                      <div className="space-y-2">
                        <Label className="text-xs">Navbar Style</Label>
                        <Select value={navbarStyle} onValueChange={setNavbarStyle}>
                          <SelectTrigger className="h-8">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            {navbarStyles.map((style) => (
                              <SelectItem key={style.id} value={style.id}>
                                <div>
                                  <div className="font-medium text-xs">{style.name}</div>
                                  <div className="text-xs text-muted-foreground">{style.description}</div>
                                </div>
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>

                      <div className="space-y-2">
                        <Label className="text-xs">Hero Section</Label>
                        <Select value={heroSection} onValueChange={setHeroSection}>
                          <SelectTrigger className="h-8">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            {heroSections.map((hero) => (
                              <SelectItem key={hero.id} value={hero.id}>
                                <div>
                                  <div className="font-medium text-xs">{hero.name}</div>
                                  <div className="text-xs text-muted-foreground">{hero.description}</div>
                                </div>
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>

                      <div className="space-y-2">
                        <Label className="text-xs">Footer Style</Label>
                        <Select value={footerStyle} onValueChange={setFooterStyle}>
                          <SelectTrigger className="h-8">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            {footerStyles.map((footer) => (
                              <SelectItem key={footer.id} value={footer.id}>
                                <div>
                                  <div className="font-medium text-xs">{footer.name}</div>
                                  <div className="text-xs text-muted-foreground">{footer.description}</div>
                                </div>
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                    </div>

                    {/* Content Settings */}
                    <div className="space-y-3">
                      <Label className="text-sm font-semibold">Content</Label>

                      <div className="space-y-2">
                        <Label className="text-xs">Site Name</Label>
                        <Input
                          value={siteName}
                          onChange={(e) => setSiteName(e.target.value)}
                          placeholder="Your Brand"
                          className="h-8"
                        />
                      </div>

                      <div className="space-y-2">
                        <Label className="text-xs">Tagline</Label>
                        <Input
                          value={tagline}
                          onChange={(e) => setTagline(e.target.value)}
                          placeholder="Your tagline"
                          className="h-8"
                        />
                      </div>

                      <div className="space-y-2">
                        <Label className="text-xs">Description</Label>
                        <Textarea
                          value={description}
                          onChange={(e) => setDescription(e.target.value)}
                          placeholder="Site description"
                          className="h-16 text-xs"
                        />
                      </div>
                    </div>

                    {/* Responsive Settings */}
                    <div className="space-y-3">
                      <Label className="text-sm font-semibold">Responsive Design</Label>

                      <div className="grid grid-cols-3 gap-1">
                        <Button
                          variant={previewDevice === "mobile" ? "default" : "outline"}
                          size="sm"
                          onClick={() => setPreviewDevice("mobile")}
                          className="h-8 text-xs"
                        >
                          <Smartphone className="h-3 w-3" />
                        </Button>
                        <Button
                          variant={previewDevice === "tablet" ? "default" : "outline"}
                          size="sm"
                          onClick={() => setPreviewDevice("tablet")}
                          className="h-8 text-xs"
                        >
                          <Tablet className="h-3 w-3" />
                        </Button>
                        <Button
                          variant={previewDevice === "desktop" ? "default" : "outline"}
                          size="sm"
                          onClick={() => setPreviewDevice("desktop")}
                          className="h-8 text-xs"
                        >
                          <Monitor className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>
                  </TabsContent>

                  <TabsContent value="design" className="space-y-4 mt-0">
                    {/* Color Schemes */}
                    <div className="space-y-3">
                      <Label className="text-sm font-semibold">Color Scheme</Label>

                      <div className="grid grid-cols-2 gap-2">
                        {colorSchemes.map((scheme) => (
                          <Button
                            key={scheme.id}
                            variant={colorScheme === scheme.id ? "default" : "outline"}
                            size="sm"
                            onClick={() => {
                              setColorScheme(scheme.id)
                              applyColorScheme(scheme)
                            }}
                            className="h-8 text-xs justify-start"
                          >
                            <div
                              className="w-3 h-3 rounded-full mr-2"
                              style={{ backgroundColor: scheme.primary }}
                            />
                            {scheme.name}
                          </Button>
                        ))}
                      </div>

                      <div className="space-y-2">
                        <Label className="text-xs">Primary Color</Label>
                        <div className="flex gap-2">
                          <Input
                            type="color"
                            value={primaryColor}
                            onChange={(e) => setPrimaryColor(e.target.value)}
                            className="w-12 h-8 p-1"
                          />
                          <Input
                            value={primaryColor}
                            onChange={(e) => setPrimaryColor(e.target.value)}
                            className="h-8 text-xs"
                          />
                        </div>
                      </div>

                      <div className="space-y-2">
                        <Label className="text-xs">Secondary Color</Label>
                        <div className="flex gap-2">
                          <Input
                            type="color"
                            value={secondaryColor}
                            onChange={(e) => setSecondaryColor(e.target.value)}
                            className="w-12 h-8 p-1"
                          />
                          <Input
                            value={secondaryColor}
                            onChange={(e) => setSecondaryColor(e.target.value)}
                            className="h-8 text-xs"
                          />
                        </div>
                      </div>

                      <div className="flex items-center justify-between">
                        <Label className="text-xs">Dark Mode</Label>
                        <Button
                          variant={darkMode ? "default" : "outline"}
                          size="sm"
                          onClick={() => setDarkMode(!darkMode)}
                          className="h-6 w-12"
                        >
                          {darkMode ? <Moon className="h-3 w-3" /> : <Sun className="h-3 w-3" />}
                        </Button>
                      </div>
                    </div>

                    {/* Typography */}
                    <div className="space-y-3">
                      <Label className="text-sm font-semibold">Typography</Label>

                      <div className="space-y-2">
                        <Label className="text-xs">Font Family</Label>
                        <Select value={fontFamily} onValueChange={setFontFamily}>
                          <SelectTrigger className="h-8">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            {fontFamilies.map((font) => (
                              <SelectItem key={font.id} value={font.id}>
                                <div>
                                  <div className="font-medium text-xs">{font.name}</div>
                                  <div className="text-xs text-muted-foreground">{font.description}</div>
                                </div>
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>

                      <div className="space-y-2">
                        <Label className="text-xs">Font Size: {fontSize[0]}px</Label>
                        <Slider
                          value={fontSize}
                          onValueChange={setFontSize}
                          max={24}
                          min={12}
                          step={1}
                          className="w-full"
                        />
                      </div>

                      <div className="space-y-2">
                        <Label className="text-xs">Line Height: {lineHeight[0]}</Label>
                        <Slider
                          value={lineHeight}
                          onValueChange={setLineHeight}
                          max={2.5}
                          min={1}
                          step={0.1}
                          className="w-full"
                        />
                      </div>

                      <div className="space-y-2">
                        <Label className="text-xs">Font Weight</Label>
                        <Select value={fontWeight} onValueChange={setFontWeight}>
                          <SelectTrigger className="h-8">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="300">Light (300)</SelectItem>
                            <SelectItem value="400">Regular (400)</SelectItem>
                            <SelectItem value="500">Medium (500)</SelectItem>
                            <SelectItem value="600">Semibold (600)</SelectItem>
                            <SelectItem value="700">Bold (700)</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>

                    {/* Animations & Effects */}
                    <div className="space-y-3">
                      <Label className="text-sm font-semibold">Animations</Label>

                      <div className="space-y-2">
                        <Label className="text-xs">Animation Type</Label>
                        <Select value={animations} onValueChange={setAnimations}>
                          <SelectTrigger className="h-8">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            {animationTypes.map((animation) => (
                              <SelectItem key={animation.id} value={animation.id}>
                                <div>
                                  <div className="font-medium text-xs">{animation.name}</div>
                                  <div className="text-xs text-muted-foreground">{animation.description}</div>
                                </div>
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                    </div>

                    {/* Social Media */}
                    <div className="space-y-3">
                      <Label className="text-sm font-semibold">Social Media</Label>

                      <div className="space-y-2">
                        <div className="flex items-center gap-2">
                          <Facebook className="h-3 w-3" />
                          <Input
                            value={facebook}
                            onChange={(e) => setFacebook(e.target.value)}
                            placeholder="Facebook URL"
                            className="h-8 text-xs"
                          />
                        </div>

                        <div className="flex items-center gap-2">
                          <Twitter className="h-3 w-3" />
                          <Input
                            value={twitter}
                            onChange={(e) => setTwitter(e.target.value)}
                            placeholder="Twitter URL"
                            className="h-8 text-xs"
                          />
                        </div>

                        <div className="flex items-center gap-2">
                          <Instagram className="h-3 w-3" />
                          <Input
                            value={instagram}
                            onChange={(e) => setInstagram(e.target.value)}
                            placeholder="Instagram URL"
                            className="h-8 text-xs"
                          />
                        </div>

                        <div className="flex items-center gap-2">
                          <Linkedin className="h-3 w-3" />
                          <Input
                            value={linkedin}
                            onChange={(e) => setLinkedin(e.target.value)}
                            placeholder="LinkedIn URL"
                            className="h-8 text-xs"
                          />
                        </div>
                      </div>
                    </div>

                    {/* Contact Information */}
                    <div className="space-y-3">
                      <Label className="text-sm font-semibold">Contact Info</Label>

                      <div className="space-y-2">
                        <div className="flex items-center gap-2">
                          <Mail className="h-3 w-3" />
                          <Input
                            value={email}
                            onChange={(e) => setEmail(e.target.value)}
                            placeholder="Email address"
                            className="h-8 text-xs"
                          />
                        </div>

                        <div className="flex items-center gap-2">
                          <Phone className="h-3 w-3" />
                          <Input
                            value={phone}
                            onChange={(e) => setPhone(e.target.value)}
                            placeholder="Phone number"
                            className="h-8 text-xs"
                          />
                        </div>

                        <div className="flex items-center gap-2">
                          <MapPin className="h-3 w-3" />
                          <Input
                            value={address}
                            onChange={(e) => setAddress(e.target.value)}
                            placeholder="Address"
                            className="h-8 text-xs"
                          />
                        </div>
                      </div>
                    </div>

                    {/* Advanced Features */}
                    <div className="space-y-3">
                      <Label className="text-sm font-semibold">Advanced</Label>

                      <div className="space-y-2">
                        <Label className="text-xs">SEO Title</Label>
                        <Input
                          value={metaTitle}
                          onChange={(e) => setMetaTitle(e.target.value)}
                          placeholder="SEO title"
                          className="h-8 text-xs"
                        />
                      </div>

                      <div className="space-y-2">
                        <Label className="text-xs">Meta Description</Label>
                        <Textarea
                          value={metaDescription}
                          onChange={(e) => setMetaDescription(e.target.value)}
                          placeholder="Meta description"
                          className="h-16 text-xs"
                        />
                      </div>

                      <div className="space-y-2">
                        <Label className="text-xs">Custom CSS</Label>
                        <Textarea
                          value={customCSS}
                          onChange={(e) => setCustomCSS(e.target.value)}
                          placeholder="/* Custom CSS */"
                          className="h-20 text-xs font-mono"
                        />
                      </div>
                    </div>
                  </TabsContent>
                </div>
              </Tabs>

              {!user && (
                <div className="mt-4 p-3 bg-muted rounded-lg">
                  <p className="text-xs text-muted-foreground">
                    Please login to save your customizations
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Live Preview */}
        <div className="lg:col-span-3">
          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <div>
                  <CardTitle className="text-lg flex items-center gap-2">
                    <Eye className="h-5 w-5" />
                    Live Preview
                  </CardTitle>
                  <CardDescription>
                    See your changes in real-time across devices
                  </CardDescription>
                </div>
                <div className="flex gap-2">
                  <Badge variant="outline" className="text-xs">
                    {previewDevice === "mobile" && "375px"}
                    {previewDevice === "tablet" && "768px"}
                    {previewDevice === "desktop" && "1200px"}
                  </Badge>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div
                className={`
                  border rounded-lg bg-background min-h-[600px] mx-auto transition-all duration-300
                  ${previewDevice === "mobile" ? "max-w-[375px]" : ""}
                  ${previewDevice === "tablet" ? "max-w-[768px]" : ""}
                  ${previewDevice === "desktop" ? "max-w-full" : ""}
                  ${darkMode ? "dark bg-gray-900 text-white" : "bg-white"}
                `}
                style={{
                  fontFamily: fontFamily === "inter" ? "Inter, sans-serif" :
                             fontFamily === "roboto" ? "Roboto, sans-serif" :
                             fontFamily === "opensans" ? "Open Sans, sans-serif" :
                             fontFamily === "lato" ? "Lato, sans-serif" :
                             fontFamily === "montserrat" ? "Montserrat, sans-serif" :
                             fontFamily === "playfair" ? "Playfair Display, serif" :
                             fontFamily === "merriweather" ? "Merriweather, serif" :
                             fontFamily === "poppins" ? "Poppins, sans-serif" : "Inter, sans-serif",
                  fontSize: `${fontSize[0]}px`,
                  lineHeight: lineHeight[0],
                  fontWeight: fontWeight,
                  backgroundColor: darkMode ? "#1F2937" : backgroundColor,
                  color: darkMode ? "#F9FAFB" : textColor,
                  '--primary-color': primaryColor,
                  '--secondary-color': secondaryColor,
                  '--accent-color': accentColor
                } as React.CSSProperties}
              >
                {/* Enhanced Navbar Preview */}
                <div className="p-4 border-b" style={{ borderColor: primaryColor }}>
                  <div className="flex justify-between items-center">
                    <div className="font-bold text-lg" style={{ color: primaryColor }}>
                      {siteName}
                    </div>
                    <div className={`flex gap-4 ${previewDevice === "mobile" ? "hidden" : ""}`}>
                      <span className="hover:opacity-80 cursor-pointer">Home</span>
                      <span className="hover:opacity-80 cursor-pointer">About</span>
                      <span className="hover:opacity-80 cursor-pointer">Services</span>
                      <span className="hover:opacity-80 cursor-pointer">Contact</span>
                    </div>
                    {previewDevice === "mobile" && (
                      <div className="w-6 h-6 flex flex-col justify-center">
                        <div className="w-full h-0.5 bg-current mb-1"></div>
                        <div className="w-full h-0.5 bg-current mb-1"></div>
                        <div className="w-full h-0.5 bg-current"></div>
                      </div>
                    )}
                  </div>
                </div>

                {/* Enhanced Hero Preview */}
                <div className="p-6" style={{ backgroundColor: `${primaryColor}10` }}>
                  <div className="text-center">
                    <h1
                      className="font-bold mb-4"
                      style={{
                        color: primaryColor,
                        fontSize: previewDevice === "mobile" ? "2rem" : previewDevice === "tablet" ? "2.5rem" : "3rem"
                      }}
                    >
                      {siteName}
                    </h1>
                    <p
                      className="mb-6"
                      style={{
                        color: secondaryColor,
                        fontSize: previewDevice === "mobile" ? "1rem" : "1.25rem"
                      }}
                    >
                      {tagline}
                    </p>
                    <p className="mb-8 max-w-2xl mx-auto opacity-80">
                      {description}
                    </p>
                    <button
                      className="px-6 py-3 rounded-lg font-semibold text-white transition-all hover:scale-105"
                      style={{
                        backgroundColor: primaryColor,
                        fontSize: previewDevice === "mobile" ? "0.875rem" : "1rem"
                      }}
                    >
                      Get Started
                    </button>
                  </div>
                </div>

                {/* Features Section */}
                <div className="p-6">
                  <h2
                    className="text-2xl font-bold text-center mb-8"
                    style={{ color: primaryColor }}
                  >
                    Features
                  </h2>
                  <div className={`grid gap-6 ${previewDevice === "mobile" ? "grid-cols-1" : previewDevice === "tablet" ? "grid-cols-2" : "grid-cols-3"}`}>
                    {[1, 2, 3].map((i) => (
                      <div
                        key={i}
                        className="p-4 rounded-lg border text-center"
                        style={{
                          borderColor: accentColor,
                          backgroundColor: darkMode ? "#374151" : "#F9FAFB"
                        }}
                      >
                        <div
                          className="w-12 h-12 rounded-full mb-4 flex items-center justify-center mx-auto"
                          style={{ backgroundColor: accentColor }}
                        >
                          <Zap className="h-6 w-6 text-white" />
                        </div>
                        <h3 className="font-semibold mb-2">Feature {i}</h3>
                        <p className="text-sm opacity-80">
                          Amazing feature description that showcases capabilities.
                        </p>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Contact Section */}
                {(email || phone || address) && (
                  <div className="p-6" style={{ backgroundColor: `${secondaryColor}10` }}>
                    <h2
                      className="text-2xl font-bold text-center mb-6"
                      style={{ color: secondaryColor }}
                    >
                      Contact Us
                    </h2>
                    <div className={`grid gap-4 ${previewDevice === "mobile" ? "grid-cols-1" : "grid-cols-3"}`}>
                      {email && (
                        <div className="flex items-center gap-3 justify-center">
                          <Mail className="h-5 w-5" style={{ color: primaryColor }} />
                          <span className="text-sm">{email}</span>
                        </div>
                      )}
                      {phone && (
                        <div className="flex items-center gap-3 justify-center">
                          <Phone className="h-5 w-5" style={{ color: primaryColor }} />
                          <span className="text-sm">{phone}</span>
                        </div>
                      )}
                      {address && (
                        <div className="flex items-center gap-3 justify-center">
                          <MapPin className="h-5 w-5" style={{ color: primaryColor }} />
                          <span className="text-sm">{address}</span>
                        </div>
                      )}
                    </div>
                  </div>
                )}

                {/* Social Media Section */}
                {(facebook || twitter || instagram || linkedin) && (
                  <div className="p-6 text-center">
                    <h3 className="text-lg font-semibold mb-4">Follow Us</h3>
                    <div className="flex justify-center gap-4">
                      {facebook && (
                        <div
                          className="w-10 h-10 rounded-full flex items-center justify-center cursor-pointer hover:scale-110 transition-transform"
                          style={{ backgroundColor: primaryColor }}
                        >
                          <Facebook className="h-5 w-5 text-white" />
                        </div>
                      )}
                      {twitter && (
                        <div
                          className="w-10 h-10 rounded-full flex items-center justify-center cursor-pointer hover:scale-110 transition-transform"
                          style={{ backgroundColor: primaryColor }}
                        >
                          <Twitter className="h-5 w-5 text-white" />
                        </div>
                      )}
                      {instagram && (
                        <div
                          className="w-10 h-10 rounded-full flex items-center justify-center cursor-pointer hover:scale-110 transition-transform"
                          style={{ backgroundColor: primaryColor }}
                        >
                          <Instagram className="h-5 w-5 text-white" />
                        </div>
                      )}
                      {linkedin && (
                        <div
                          className="w-10 h-10 rounded-full flex items-center justify-center cursor-pointer hover:scale-110 transition-transform"
                          style={{ backgroundColor: primaryColor }}
                        >
                          <Linkedin className="h-5 w-5 text-white" />
                        </div>
                      )}
                    </div>
                  </div>
                )}

                {/* Enhanced Footer Preview */}
                <div
                  className="mt-8 p-6 border-t text-center"
                  style={{
                    borderColor: accentColor,
                    backgroundColor: darkMode ? "#111827" : "#F3F4F6"
                  }}
                >
                  <p className="text-sm opacity-80">
                    © 2024 {siteName}. All rights reserved.
                  </p>
                  {(facebook || twitter || instagram || linkedin) && (
                    <div className="flex justify-center gap-4 mt-4">
                      <span className="text-xs opacity-60">Follow us:</span>
                      {facebook && <span className="text-xs hover:opacity-80 cursor-pointer">Facebook</span>}
                      {twitter && <span className="text-xs hover:opacity-80 cursor-pointer">Twitter</span>}
                      {instagram && <span className="text-xs hover:opacity-80 cursor-pointer">Instagram</span>}
                      {linkedin && <span className="text-xs hover:opacity-80 cursor-pointer">LinkedIn</span>}
                    </div>
                  )}
                </div>
              </div>

              {/* Configuration Summary */}
              <div className="mt-6 p-4 bg-muted rounded-lg">
                <h4 className="font-semibold mb-2">Current Configuration</h4>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                  <div>
                    <span className="text-muted-foreground">Layout:</span>
                    <p className="font-medium">{layoutStyle}</p>
                  </div>
                  <div>
                    <span className="text-muted-foreground">Theme:</span>
                    <p className="font-medium">{colorScheme} {darkMode && "(Dark)"}</p>
                  </div>
                  <div>
                    <span className="text-muted-foreground">Font:</span>
                    <p className="font-medium">{fontFamily} {fontSize[0]}px</p>
                  </div>
                  <div>
                    <span className="text-muted-foreground">Animation:</span>
                    <p className="font-medium">{animations}</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Export Options */}
      <Card>
        <CardHeader>
          <CardTitle>Export Options</CardTitle>
          <CardDescription>
            Download your customized template
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex gap-4">
            <Button>
              <Download className="h-4 w-4 mr-2" />
              Download HTML/CSS
            </Button>
            <Button variant="outline">
              <Download className="h-4 w-4 mr-2" />
              Download React Components
            </Button>
            <Button variant="outline">
              <Download className="h-4 w-4 mr-2" />
              Export as PDF
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
