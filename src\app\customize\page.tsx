"use client"

import { useState } from "react"
import { <PERSON>, Card<PERSON>ontent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>List, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { Slider } from "@/components/ui/slider"
import { 
  Palette, 
  Type, 
  Layout, 
  Image as ImageIcon, 
  Save, 
  Download,
  Eye,
  Undo,
  Redo
} from "lucide-react"

export default function CustomizePage() {
  const [selectedColor, setSelectedColor] = useState("#3B82F6")
  const [fontSize, setFontSize] = useState([16])
  const [spacing, setSpacing] = useState([8])

  const colorPresets = [
    "#3B82F6", "#EF4444", "#10B981", "#F59E0B", 
    "#8B5CF6", "#EC4899", "#06B6D4", "#84CC16"
  ]

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Live Preview Customizer</h1>
          <p className="text-muted-foreground">
            Customize your template in real-time with our visual editor
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" size="sm">
            <Undo className="h-4 w-4 mr-2" />
            Undo
          </Button>
          <Button variant="outline" size="sm">
            <Redo className="h-4 w-4 mr-2" />
            Redo
          </Button>
          <Button variant="outline" size="sm">
            <Eye className="h-4 w-4 mr-2" />
            Preview
          </Button>
          <Button size="sm">
            <Save className="h-4 w-4 mr-2" />
            Save
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Customization Panel */}
        <div className="lg:col-span-1">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Customization Panel</CardTitle>
              <CardDescription>
                Adjust your template settings
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Tabs defaultValue="colors" className="w-full">
                <TabsList className="grid w-full grid-cols-4">
                  <TabsTrigger value="colors" className="p-2">
                    <Palette className="h-4 w-4" />
                  </TabsTrigger>
                  <TabsTrigger value="typography" className="p-2">
                    <Type className="h-4 w-4" />
                  </TabsTrigger>
                  <TabsTrigger value="layout" className="p-2">
                    <Layout className="h-4 w-4" />
                  </TabsTrigger>
                  <TabsTrigger value="media" className="p-2">
                    <ImageIcon className="h-4 w-4" />
                  </TabsTrigger>
                </TabsList>

                <TabsContent value="colors" className="space-y-4">
                  <div>
                    <Label>Primary Color</Label>
                    <div className="flex gap-2 mt-2">
                      {colorPresets.map((color) => (
                        <button
                          key={color}
                          className="w-8 h-8 rounded-full border-2 border-white shadow-md"
                          style={{ backgroundColor: color }}
                          onClick={() => setSelectedColor(color)}
                        />
                      ))}
                    </div>
                    <Input
                      type="color"
                      value={selectedColor}
                      onChange={(e) => setSelectedColor(e.target.value)}
                      className="mt-2 h-10"
                    />
                  </div>
                  <div>
                    <Label>Background Color</Label>
                    <Input type="color" defaultValue="#FFFFFF" className="mt-2 h-10" />
                  </div>
                  <div>
                    <Label>Text Color</Label>
                    <Input type="color" defaultValue="#000000" className="mt-2 h-10" />
                  </div>
                </TabsContent>

                <TabsContent value="typography" className="space-y-4">
                  <div>
                    <Label>Font Family</Label>
                    <select className="w-full mt-2 p-2 border rounded-md">
                      <option>Inter</option>
                      <option>Roboto</option>
                      <option>Open Sans</option>
                      <option>Lato</option>
                    </select>
                  </div>
                  <div>
                    <Label>Font Size: {fontSize[0]}px</Label>
                    <Slider
                      value={fontSize}
                      onValueChange={setFontSize}
                      max={24}
                      min={12}
                      step={1}
                      className="mt-2"
                    />
                  </div>
                  <div>
                    <Label>Line Height</Label>
                    <Slider
                      defaultValue={[1.5]}
                      max={2}
                      min={1}
                      step={0.1}
                      className="mt-2"
                    />
                  </div>
                </TabsContent>

                <TabsContent value="layout" className="space-y-4">
                  <div>
                    <Label>Container Width</Label>
                    <select className="w-full mt-2 p-2 border rounded-md">
                      <option>Full Width</option>
                      <option>1200px</option>
                      <option>1024px</option>
                      <option>768px</option>
                    </select>
                  </div>
                  <div>
                    <Label>Spacing: {spacing[0]}px</Label>
                    <Slider
                      value={spacing}
                      onValueChange={setSpacing}
                      max={32}
                      min={0}
                      step={4}
                      className="mt-2"
                    />
                  </div>
                  <div>
                    <Label>Border Radius</Label>
                    <Slider
                      defaultValue={[8]}
                      max={20}
                      min={0}
                      step={2}
                      className="mt-2"
                    />
                  </div>
                </TabsContent>

                <TabsContent value="media" className="space-y-4">
                  <div>
                    <Label>Upload Image</Label>
                    <Input type="file" accept="image/*" className="mt-2" />
                  </div>
                  <div>
                    <Label>Image Position</Label>
                    <select className="w-full mt-2 p-2 border rounded-md">
                      <option>Center</option>
                      <option>Top</option>
                      <option>Bottom</option>
                      <option>Left</option>
                      <option>Right</option>
                    </select>
                  </div>
                  <div>
                    <Label>Image Size</Label>
                    <select className="w-full mt-2 p-2 border rounded-md">
                      <option>Cover</option>
                      <option>Contain</option>
                      <option>Auto</option>
                    </select>
                  </div>
                </TabsContent>
              </Tabs>
            </CardContent>
          </Card>
        </div>

        {/* Live Preview */}
        <div className="lg:col-span-3">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Live Preview</CardTitle>
              <CardDescription>
                See your changes in real-time
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div 
                className="min-h-[600px] border-2 border-dashed border-muted-foreground/25 rounded-lg p-8"
                style={{ 
                  backgroundColor: "#FFFFFF",
                  fontSize: `${fontSize[0]}px`,
                  padding: `${spacing[0] * 2}px`
                }}
              >
                <div className="space-y-6">
                  <div 
                    className="text-center p-6 rounded-lg"
                    style={{ backgroundColor: selectedColor, color: "white" }}
                  >
                    <h1 className="text-3xl font-bold mb-2">Your Template Title</h1>
                    <p className="text-lg opacity-90">
                      This is your customizable template preview
                    </p>
                  </div>
                  
                  <div className="grid md:grid-cols-2 gap-6">
                    <div className="space-y-4">
                      <h2 className="text-2xl font-semibold">Section Title</h2>
                      <p className="text-muted-foreground">
                        This is sample content that will update as you customize your template. 
                        You can see how your color and typography changes affect the overall design.
                      </p>
                      <Button style={{ backgroundColor: selectedColor }}>
                        Call to Action
                      </Button>
                    </div>
                    
                    <div className="bg-muted rounded-lg p-6 flex items-center justify-center">
                      <div className="text-center text-muted-foreground">
                        <ImageIcon className="h-12 w-12 mx-auto mb-2" />
                        <p>Image Placeholder</p>
                      </div>
                    </div>
                  </div>
                  
                  <div className="grid md:grid-cols-3 gap-4">
                    {[1, 2, 3].map((i) => (
                      <div key={i} className="border rounded-lg p-4">
                        <h3 className="font-semibold mb-2">Feature {i}</h3>
                        <p className="text-sm text-muted-foreground">
                          Description of feature {i} goes here.
                        </p>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Export Options */}
      <Card>
        <CardHeader>
          <CardTitle>Export Options</CardTitle>
          <CardDescription>
            Download your customized template
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex gap-4">
            <Button>
              <Download className="h-4 w-4 mr-2" />
              Download HTML/CSS
            </Button>
            <Button variant="outline">
              <Download className="h-4 w-4 mr-2" />
              Download React Components
            </Button>
            <Button variant="outline">
              <Download className="h-4 w-4 mr-2" />
              Export as PDF
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
