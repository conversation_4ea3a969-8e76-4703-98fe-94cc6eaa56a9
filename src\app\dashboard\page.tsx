"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { createClient } from "@/lib/supabase/client"
import { Database } from "@/lib/database.types"
import { toast } from "sonner"
import { useRouter } from "next/navigation"
import { FileText, Settings, ShoppingBag, User } from "lucide-react"

type Purchase = Database['public']['Tables']['purchases']['Row'] & {
  templates: Database['public']['Tables']['templates']['Row']
}

type Customization = Database['public']['Tables']['customizations']['Row']

export default function DashboardPage() {
  const [user, setUser] = useState<any>(null)
  const [purchases, setPurchases] = useState<Purchase[]>([])
  const [customizations, setCustomizations] = useState<Customization[]>([])
  const [loading, setLoading] = useState(true)

  const supabase = createClient()
  const router = useRouter()

  useEffect(() => {
    checkUser()
  }, [])

  const checkUser = async () => {
    try {
      const { data: { user }, error } = await supabase.auth.getUser()
      
      if (error || !user) {
        router.push('/login')
        return
      }

      setUser(user)
      await Promise.all([
        fetchPurchases(user.id),
        fetchCustomizations(user.id)
      ])
    } catch (error) {
      console.error('Error checking user:', error)
      toast.error('Failed to load user data')
    } finally {
      setLoading(false)
    }
  }

  const fetchPurchases = async (userId: string) => {
    try {
      const { data, error } = await supabase
        .from('purchases')
        .select(`
          *,
          templates (*)
        `)
        .eq('user_id', userId)
        .order('created_at', { ascending: false })

      if (error) throw error
      setPurchases(data || [])
    } catch (error) {
      console.error('Error fetching purchases:', error)
      toast.error('Failed to load purchases')
    }
  }

  const fetchCustomizations = async (userId: string) => {
    try {
      const { data, error } = await supabase
        .from('customizations')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false })

      if (error) throw error
      setCustomizations(data || [])
    } catch (error) {
      console.error('Error fetching customizations:', error)
      toast.error('Failed to load customizations')
    }
  }

  const handleSignOut = async () => {
    await supabase.auth.signOut()
    router.push('/')
  }

  if (loading) {
    return (
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Dashboard</h1>
          <p className="text-muted-foreground">Loading your dashboard...</p>
        </div>
      </div>
    )
  }

  if (!user) {
    return null
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Dashboard</h1>
          <p className="text-muted-foreground">
            Welcome back, {user.user_metadata?.full_name || user.email}!
          </p>
        </div>
        <Button variant="outline" onClick={handleSignOut}>
          Sign Out
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Templates Purchased</CardTitle>
            <ShoppingBag className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{purchases.length}</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Customizations</CardTitle>
            <Settings className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{customizations.length}</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Spent</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              ₹{purchases.reduce((total, purchase) => total + purchase.amount, 0)}
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Account Status</CardTitle>
            <User className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">Active</div>
          </CardContent>
        </Card>
      </div>

      {/* Purchased Templates */}
      <Card>
        <CardHeader>
          <CardTitle>Your Purchased Templates</CardTitle>
          <CardDescription>
            Templates you have purchased and can download
          </CardDescription>
        </CardHeader>
        <CardContent>
          {purchases.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-muted-foreground">No templates purchased yet.</p>
              <Button className="mt-4" onClick={() => router.push('/templates')}>
                Browse Templates
              </Button>
            </div>
          ) : (
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
              {purchases.map((purchase) => (
                <Card key={purchase.id}>
                  <CardHeader>
                    <CardTitle className="text-lg">{purchase.templates.title}</CardTitle>
                    <CardDescription>
                      Purchased on {new Date(purchase.created_at).toLocaleDateString()}
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="flex justify-between items-center mb-4">
                      <Badge variant="secondary">{purchase.templates.category}</Badge>
                      <span className="font-bold">₹{purchase.amount}</span>
                    </div>
                    <div className="flex gap-2">
                      <Button size="sm" className="flex-1">
                        Download
                      </Button>
                      {purchase.templates.preview_url && (
                        <Button 
                          variant="outline" 
                          size="sm"
                          onClick={() => window.open(purchase.templates.preview_url!, '_blank')}
                        >
                          Preview
                        </Button>
                      )}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Saved Customizations */}
      <Card>
        <CardHeader>
          <CardTitle>Your Customizations</CardTitle>
          <CardDescription>
            Template customizations you have saved
          </CardDescription>
        </CardHeader>
        <CardContent>
          {customizations.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-muted-foreground">No customizations saved yet.</p>
              <Button className="mt-4" onClick={() => router.push('/customize')}>
                Start Customizing
              </Button>
            </div>
          ) : (
            <div className="space-y-4">
              {customizations.map((customization) => (
                <Card key={customization.id}>
                  <CardContent className="pt-6">
                    <div className="flex justify-between items-start">
                      <div>
                        <h4 className="font-semibold mb-2">
                          Customization #{customization.id.slice(0, 8)}
                        </h4>
                        <div className="text-sm text-muted-foreground space-y-1">
                          <p>Navbar: {customization.navbar_style}</p>
                          <p>Hero: {customization.hero_section}</p>
                          <p>Footer: {customization.footer_style}</p>
                          <p>Created: {new Date(customization.created_at).toLocaleDateString()}</p>
                        </div>
                      </div>
                      <div className="flex gap-2">
                        <Button 
                          size="sm" 
                          variant="outline"
                          onClick={() => router.push('/customize')}
                        >
                          Edit
                        </Button>
                        <Button size="sm">
                          Use Template
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
