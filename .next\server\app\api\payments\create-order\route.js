const CHUNK_PUBLIC_PATH = "server/app/api/payments/create-order/route.js";
const runtime = require("../../../../chunks/[turbopack]_runtime.js");
runtime.loadChunk("server/chunks/node_modules_88239e18._.js");
runtime.loadChunk("server/chunks/node_modules_next_81eafd7d._.js");
runtime.loadChunk("server/chunks/node_modules_mime-db_9ebaabbe._.js");
runtime.loadChunk("server/chunks/node_modules_axios_dist_node_axios_cjs_48309a18._.js");
runtime.loadChunk("server/chunks/node_modules_tr46_816df9d9._.js");
runtime.loadChunk("server/chunks/node_modules_@supabase_auth-js_dist_module_fbd19a5c._.js");
runtime.loadChunk("server/chunks/node_modules_c1df36a4._.js");
runtime.loadChunk("server/chunks/[root-of-the-server]__856811ae._.js");
runtime.getOrInstantiateRuntimeModule("[project]/.next-internal/server/app/api/payments/create-order/route/actions.js [app-rsc] (server actions loader, ecmascript)", CHUNK_PUBLIC_PATH);
runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/src/app/api/payments/create-order/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)", CHUNK_PUBLIC_PATH);
module.exports = runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/src/app/api/payments/create-order/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)", CHUNK_PUBLIC_PATH).exports;
