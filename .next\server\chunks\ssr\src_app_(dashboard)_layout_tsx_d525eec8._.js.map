{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Documents/kaleidonex/src/app/%28dashboard%29/layout.tsx"], "sourcesContent": ["import { MainLayout } from \"@/components/layout/main-layout\"\n\nexport default function DashboardLayout({\n  children,\n}: {\n  children: React.ReactNode\n}) {\n  return <MainLayout>{children}</MainLayout>\n}\n"], "names": [], "mappings": ";;;;AAAA;;;AAEe,SAAS,gBAAgB,EACtC,QAAQ,EAGT;IACC,qBAAO,8OAAC,8IAAA,CAAA,aAAU;kBAAE;;;;;;AACtB", "debugId": null}}]}