{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Documents/kaleidonex/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 104, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Documents/kaleidonex/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Label({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  return (\n    <LabelPrimitive.Root\n      data-slot=\"label\"\n      className={cn(\n        \"flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,EACb,SAAS,EACT,GAAG,OAC8C;IACjD,qBACE,8OAAC,iKAAA,CAAA,OAAmB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uNACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 132, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Documents/kaleidonex/src/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check<PERSON><PERSON>, ChevronDownIcon, ChevronUpIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Select({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Root>) {\n  return <SelectPrimitive.Root data-slot=\"select\" {...props} />\n}\n\nfunction SelectGroup({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Group>) {\n  return <SelectPrimitive.Group data-slot=\"select-group\" {...props} />\n}\n\nfunction SelectValue({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Value>) {\n  return <SelectPrimitive.Value data-slot=\"select-value\" {...props} />\n}\n\nfunction SelectTrigger({\n  className,\n  size = \"default\",\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Trigger> & {\n  size?: \"sm\" | \"default\"\n}) {\n  return (\n    <SelectPrimitive.Trigger\n      data-slot=\"select-trigger\"\n      data-size={size}\n      className={cn(\n        \"border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <SelectPrimitive.Icon asChild>\n        <ChevronDownIcon className=\"size-4 opacity-50\" />\n      </SelectPrimitive.Icon>\n    </SelectPrimitive.Trigger>\n  )\n}\n\nfunction SelectContent({\n  className,\n  children,\n  position = \"popper\",\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Content>) {\n  return (\n    <SelectPrimitive.Portal>\n      <SelectPrimitive.Content\n        data-slot=\"select-content\"\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md\",\n          position === \"popper\" &&\n            \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n          className\n        )}\n        position={position}\n        {...props}\n      >\n        <SelectScrollUpButton />\n        <SelectPrimitive.Viewport\n          className={cn(\n            \"p-1\",\n            position === \"popper\" &&\n              \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1\"\n          )}\n        >\n          {children}\n        </SelectPrimitive.Viewport>\n        <SelectScrollDownButton />\n      </SelectPrimitive.Content>\n    </SelectPrimitive.Portal>\n  )\n}\n\nfunction SelectLabel({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Label>) {\n  return (\n    <SelectPrimitive.Label\n      data-slot=\"select-label\"\n      className={cn(\"text-muted-foreground px-2 py-1.5 text-xs\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Item>) {\n  return (\n    <SelectPrimitive.Item\n      data-slot=\"select-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"absolute right-2 flex size-3.5 items-center justify-center\">\n        <SelectPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </SelectPrimitive.ItemIndicator>\n      </span>\n      <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n    </SelectPrimitive.Item>\n  )\n}\n\nfunction SelectSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Separator>) {\n  return (\n    <SelectPrimitive.Separator\n      data-slot=\"select-separator\"\n      className={cn(\"bg-border pointer-events-none -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectScrollUpButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollUpButton>) {\n  return (\n    <SelectPrimitive.ScrollUpButton\n      data-slot=\"select-scroll-up-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronUpIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollUpButton>\n  )\n}\n\nfunction SelectScrollDownButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollDownButton>) {\n  return (\n    <SelectPrimitive.ScrollDownButton\n      data-slot=\"select-scroll-down-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronDownIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollDownButton>\n  )\n}\n\nexport {\n  Select,\n  SelectContent,\n  SelectGroup,\n  SelectItem,\n  SelectLabel,\n  SelectScrollDownButton,\n  SelectScrollUpButton,\n  SelectSeparator,\n  SelectTrigger,\n  SelectValue,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,8OAAC,kKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,OAAO,SAAS,EAChB,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,8OAAC,kKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,aAAW;QACX,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gzBACA;QAED,GAAG,KAAK;;YAER;0BACD,8OAAC,kKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,8OAAC,wNAAA,CAAA,kBAAe;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAInC;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,WAAW,QAAQ,EACnB,GAAG,OACkD;IACrD,qBACE,8OAAC,kKAAA,CAAA,SAAsB;kBACrB,cAAA,8OAAC,kKAAA,CAAA,UAAuB;YACtB,aAAU;YACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ijBACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,8OAAC;;;;;8BACD,8OAAC,kKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,8OAAC;;;;;;;;;;;;;;;;AAIT;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,kKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAClB,SAAS,EACT,QAAQ,EACR,GAAG,OAC+C;IAClD,qBACE,8OAAC,kKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,6aACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,kKAAA,CAAA,gBAA6B;8BAC5B,cAAA,8OAAC,wMAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGzB,8OAAC,kKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAGjC;AAEA,SAAS,gBAAgB,EACvB,SAAS,EACT,GAAG,OACoD;IACvD,qBACE,8OAAC,kKAAA,CAAA,YAAyB;QACxB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iDAAiD;QAC9D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OACyD;IAC5D,qBACE,8OAAC,kKAAA,CAAA,iBAA8B;QAC7B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,oNAAA,CAAA,gBAAa;YAAC,WAAU;;;;;;;;;;;AAG/B;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,8OAAC,kKAAA,CAAA,mBAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,wNAAA,CAAA,kBAAe;YAAC,WAAU;;;;;;;;;;;AAGjC", "debugId": null}}, {"offset": {"line": 405, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Documents/kaleidonex/src/lib/supabase/client.ts"], "sourcesContent": ["import { createBrowserClient } from '@supabase/ssr'\n\nexport function createClient() {\n  return createBrowserClient(\n    process.env.NEXT_PUBLIC_SUPABASE_URL!,\n    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!\n  )\n}\n"], "names": [], "mappings": ";;;AAAA;AAAA;;AAEO,SAAS;IACd,OAAO,CAAA,GAAA,0KAAA,CAAA,sBAAmB,AAAD,EACvB,QAAQ,GAAG,CAAC,wBAAwB,EACpC,QAAQ,GAAG,CAAC,6BAA6B;AAE7C", "debugId": null}}, {"offset": {"line": 420, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Documents/kaleidonex/src/app/customize/page.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState, useEffect } from \"react\"\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\"\nimport { But<PERSON> } from \"@/components/ui/button\"\nimport { Label } from \"@/components/ui/label\"\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from \"@/components/ui/select\"\nimport { createClient } from \"@/lib/supabase/client\"\nimport { getUser } from \"@/lib/auth\"\nimport { toast } from \"sonner\"\nimport {\n  Save,\n  Eye,\n  Mail\n} from \"lucide-react\"\n\nconst navbarStyles = [\n  { id: \"modern\", name: \"Modern\", description: \"Clean and minimal navigation\" },\n  { id: \"classic\", name: \"Classic\", description: \"Traditional horizontal menu\" },\n  { id: \"sidebar\", name: \"Sidebar\", description: \"Vertical side navigation\" },\n  { id: \"floating\", name: \"Floating\", description: \"Floating navigation bar\" }\n]\n\nconst heroSections = [\n  { id: \"hero1\", name: \"Hero with Image\", description: \"Large hero with background image\" },\n  { id: \"hero2\", name: \"Split Hero\", description: \"Text on left, image on right\" },\n  { id: \"hero3\", name: \"Centered Hero\", description: \"Centered text with call-to-action\" },\n  { id: \"hero4\", name: \"Video Hero\", description: \"Hero with background video\" }\n]\n\nconst footerStyles = [\n  { id: \"simple\", name: \"Simple\", description: \"Minimal footer with links\" },\n  { id: \"detailed\", name: \"Detailed\", description: \"Multi-column footer\" },\n  { id: \"newsletter\", name: \"Newsletter\", description: \"Footer with newsletter signup\" },\n  { id: \"social\", name: \"Social\", description: \"Footer focused on social links\" }\n]\n\nexport default function CustomizePage() {\n  const [navbarStyle, setNavbarStyle] = useState(\"modern\")\n  const [heroSection, setHeroSection] = useState(\"hero1\")\n  const [footerStyle, setFooterStyle] = useState(\"simple\")\n  const [user, setUser] = useState<any>(null)\n  const [saving, setSaving] = useState(false)\n\n  const supabase = createClient()\n\n  useEffect(() => {\n    checkUser()\n  }, [])\n\n  const checkUser = async () => {\n    const { data: { user } } = await supabase.auth.getUser()\n    setUser(user)\n  }\n\n  const saveCustomization = async () => {\n    if (!user) {\n      toast.error('Please login to save customizations')\n      return\n    }\n\n    setSaving(true)\n    try {\n      const config = {\n        navbarStyle,\n        heroSection,\n        footerStyle,\n        timestamp: new Date().toISOString()\n      }\n\n      const { error } = await supabase\n        .from('customizations')\n        .insert({\n          user_id: user.id,\n          navbar_style: navbarStyle,\n          hero_section: heroSection,\n          footer_style: footerStyle,\n          config\n        })\n\n      if (error) throw error\n\n      toast.success('Customization saved successfully!')\n    } catch (error) {\n      console.error('Error saving customization:', error)\n      toast.error('Failed to save customization')\n    } finally {\n      setSaving(false)\n    }\n  }\n\n  const contactToBuy = async () => {\n    if (!user) {\n      toast.error('Please login to contact us')\n      return\n    }\n\n    try {\n      const config = {\n        navbarStyle,\n        heroSection,\n        footerStyle,\n        timestamp: new Date().toISOString()\n      }\n\n      // Save the customization first\n      const { error: saveError } = await supabase\n        .from('customizations')\n        .insert({\n          user_id: user.id,\n          navbar_style: navbarStyle,\n          hero_section: heroSection,\n          footer_style: footerStyle,\n          config\n        })\n\n      if (saveError) throw saveError\n\n      // Create a contact request\n      const { error: contactError } = await supabase\n        .from('contact_requests')\n        .insert({\n          name: user.user_metadata?.full_name || user.email,\n          email: user.email,\n          message: `I'm interested in purchasing a custom template with the following configuration:\n\nNavbar Style: ${navbarStyles.find(n => n.id === navbarStyle)?.name}\nHero Section: ${heroSections.find(h => h.id === heroSection)?.name}\nFooter Style: ${footerStyles.find(f => f.id === footerStyle)?.name}\n\nPlease contact me with pricing and timeline information.`\n        })\n\n      if (contactError) throw contactError\n\n      toast.success('Your customization has been saved and we will contact you soon!')\n    } catch (error) {\n      console.error('Error:', error)\n      toast.error('Failed to process request')\n    }\n  }\n\n  const getNavbarStyles = (style: string) => {\n    switch (style) {\n      case \"modern\":\n        return \"bg-white shadow-sm\"\n      case \"classic\":\n        return \"bg-gray-100\"\n      case \"sidebar\":\n        return \"bg-gray-900 text-white\"\n      case \"floating\":\n        return \"bg-white shadow-lg rounded-lg mx-4 mt-4\"\n      default:\n        return \"bg-white\"\n    }\n  }\n\n  const getHeroStyles = (style: string) => {\n    switch (style) {\n      case \"hero1\":\n        return \"bg-gradient-to-r from-blue-500 to-purple-600 text-white text-center\"\n      case \"hero2\":\n        return \"bg-gray-50\"\n      case \"hero3\":\n        return \"bg-white text-center\"\n      case \"hero4\":\n        return \"bg-black text-white text-center\"\n      default:\n        return \"bg-gray-50\"\n    }\n  }\n\n  const getFooterStyles = (style: string) => {\n    switch (style) {\n      case \"simple\":\n        return \"bg-gray-100 text-center\"\n      case \"detailed\":\n        return \"bg-gray-900 text-white\"\n      case \"newsletter\":\n        return \"bg-blue-50\"\n      case \"social\":\n        return \"bg-gray-800 text-white text-center\"\n      default:\n        return \"bg-gray-100\"\n    }\n  }\n\n  const renderHeroContent = (style: string) => {\n    switch (style) {\n      case \"hero1\":\n        return (\n          <div>\n            <h1 className=\"text-4xl font-bold mb-4\">Welcome to Your Website</h1>\n            <p className=\"text-xl mb-6\">Create amazing experiences with our templates</p>\n            <Button className=\"bg-white text-blue-600 hover:bg-gray-100\">Get Started</Button>\n          </div>\n        )\n      case \"hero2\":\n        return (\n          <div className=\"grid md:grid-cols-2 gap-8 items-center\">\n            <div>\n              <h1 className=\"text-4xl font-bold mb-4\">Split Hero Section</h1>\n              <p className=\"text-lg mb-6\">Text content on the left side with image on the right</p>\n              <Button>Learn More</Button>\n            </div>\n            <div className=\"bg-gray-200 h-64 rounded-lg flex items-center justify-center\">\n              <span className=\"text-gray-500\">Hero Image</span>\n            </div>\n          </div>\n        )\n      case \"hero3\":\n        return (\n          <div>\n            <h1 className=\"text-4xl font-bold mb-4\">Centered Hero</h1>\n            <p className=\"text-lg mb-6\">Perfect for landing pages and focused messaging</p>\n            <div className=\"flex gap-4 justify-center\">\n              <Button>Primary Action</Button>\n              <Button variant=\"outline\">Secondary Action</Button>\n            </div>\n          </div>\n        )\n      case \"hero4\":\n        return (\n          <div>\n            <h1 className=\"text-4xl font-bold mb-4\">Video Hero Background</h1>\n            <p className=\"text-xl mb-6\">Engaging video background for maximum impact</p>\n            <Button className=\"bg-white text-black hover:bg-gray-100\">Watch Demo</Button>\n          </div>\n        )\n      default:\n        return <div>Hero Content</div>\n    }\n  }\n\n  const renderFooterContent = (style: string) => {\n    switch (style) {\n      case \"simple\":\n        return (\n          <div>\n            <p>&copy; 2024 Your Company. All rights reserved.</p>\n          </div>\n        )\n      case \"detailed\":\n        return (\n          <div className=\"grid md:grid-cols-4 gap-6\">\n            <div>\n              <h4 className=\"font-semibold mb-2\">Company</h4>\n              <ul className=\"space-y-1 text-sm\">\n                <li>About</li>\n                <li>Careers</li>\n                <li>Contact</li>\n              </ul>\n            </div>\n            <div>\n              <h4 className=\"font-semibold mb-2\">Products</h4>\n              <ul className=\"space-y-1 text-sm\">\n                <li>Templates</li>\n                <li>Tools</li>\n                <li>Support</li>\n              </ul>\n            </div>\n            <div>\n              <h4 className=\"font-semibold mb-2\">Resources</h4>\n              <ul className=\"space-y-1 text-sm\">\n                <li>Blog</li>\n                <li>Documentation</li>\n                <li>Help Center</li>\n              </ul>\n            </div>\n            <div>\n              <h4 className=\"font-semibold mb-2\">Legal</h4>\n              <ul className=\"space-y-1 text-sm\">\n                <li>Privacy</li>\n                <li>Terms</li>\n                <li>Cookies</li>\n              </ul>\n            </div>\n          </div>\n        )\n      case \"newsletter\":\n        return (\n          <div className=\"text-center\">\n            <h4 className=\"font-semibold mb-2\">Subscribe to our newsletter</h4>\n            <p className=\"text-sm mb-4\">Get the latest updates and offers</p>\n            <div className=\"flex gap-2 justify-center max-w-md mx-auto\">\n              <input\n                type=\"email\"\n                placeholder=\"Enter your email\"\n                className=\"flex-1 px-3 py-2 border rounded\"\n              />\n              <Button>Subscribe</Button>\n            </div>\n          </div>\n        )\n      case \"social\":\n        return (\n          <div>\n            <div className=\"flex justify-center gap-4 mb-4\">\n              <span>📘</span>\n              <span>🐦</span>\n              <span>📷</span>\n              <span>💼</span>\n            </div>\n            <p>&copy; 2024 Your Company. Follow us on social media.</p>\n          </div>\n        )\n      default:\n        return <div>Footer Content</div>\n    }\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      <div className=\"flex justify-between items-center\">\n        <div>\n          <h1 className=\"text-3xl font-bold tracking-tight\">Live Preview Customizer</h1>\n          <p className=\"text-muted-foreground\">\n            Customize your template in real-time with our visual editor\n          </p>\n        </div>\n        <div className=\"flex gap-2\">\n          <Button variant=\"outline\" size=\"sm\" onClick={saveCustomization} disabled={saving || !user}>\n            <Save className=\"h-4 w-4 mr-2\" />\n            {saving ? 'Saving...' : 'Save'}\n          </Button>\n          <Button size=\"sm\" onClick={contactToBuy} disabled={!user}>\n            <Mail className=\"h-4 w-4 mr-2\" />\n            Contact to Buy\n          </Button>\n        </div>\n      </div>\n\n      <div className=\"grid grid-cols-1 lg:grid-cols-4 gap-6\">\n        {/* Customization Panel */}\n        <div className=\"lg:col-span-1\">\n          <Card>\n            <CardHeader>\n              <CardTitle className=\"text-lg\">Customization Options</CardTitle>\n              <CardDescription>\n                Choose your template components\n              </CardDescription>\n            </CardHeader>\n            <CardContent className=\"space-y-6\">\n              {/* Navbar Style */}\n              <div className=\"space-y-2\">\n                <Label>Navbar Style</Label>\n                <Select value={navbarStyle} onValueChange={setNavbarStyle}>\n                  <SelectTrigger>\n                    <SelectValue placeholder=\"Select navbar style\" />\n                  </SelectTrigger>\n                  <SelectContent>\n                    {navbarStyles.map((style) => (\n                      <SelectItem key={style.id} value={style.id}>\n                        <div>\n                          <div className=\"font-medium\">{style.name}</div>\n                          <div className=\"text-sm text-muted-foreground\">{style.description}</div>\n                        </div>\n                      </SelectItem>\n                    ))}\n                  </SelectContent>\n                </Select>\n              </div>\n\n              {/* Hero Section */}\n              <div className=\"space-y-2\">\n                <Label>Hero Section</Label>\n                <Select value={heroSection} onValueChange={setHeroSection}>\n                  <SelectTrigger>\n                    <SelectValue placeholder=\"Select hero section\" />\n                  </SelectTrigger>\n                  <SelectContent>\n                    {heroSections.map((hero) => (\n                      <SelectItem key={hero.id} value={hero.id}>\n                        <div>\n                          <div className=\"font-medium\">{hero.name}</div>\n                          <div className=\"text-sm text-muted-foreground\">{hero.description}</div>\n                        </div>\n                      </SelectItem>\n                    ))}\n                  </SelectContent>\n                </Select>\n              </div>\n\n              {/* Footer Style */}\n              <div className=\"space-y-2\">\n                <Label>Footer Style</Label>\n                <Select value={footerStyle} onValueChange={setFooterStyle}>\n                  <SelectTrigger>\n                    <SelectValue placeholder=\"Select footer style\" />\n                  </SelectTrigger>\n                  <SelectContent>\n                    {footerStyles.map((footer) => (\n                      <SelectItem key={footer.id} value={footer.id}>\n                        <div>\n                          <div className=\"font-medium\">{footer.name}</div>\n                          <div className=\"text-sm text-muted-foreground\">{footer.description}</div>\n                        </div>\n                      </SelectItem>\n                    ))}\n                  </SelectContent>\n                </Select>\n              </div>\n\n              {!user && (\n                <div className=\"p-4 bg-muted rounded-lg\">\n                  <p className=\"text-sm text-muted-foreground\">\n                    Please login to save your customizations\n                  </p>\n                </div>\n              )}\n            </CardContent>\n          </Card>\n        </div>\n\n        {/* Live Preview */}\n        <div className=\"lg:col-span-3\">\n          <Card>\n            <CardHeader>\n              <CardTitle className=\"text-lg\">Live Preview</CardTitle>\n              <CardDescription>\n                See your template preview based on your selections\n              </CardDescription>\n            </CardHeader>\n            <CardContent>\n              <div className=\"min-h-[600px] border-2 border-dashed border-muted-foreground/25 rounded-lg overflow-hidden\">\n                {/* Navbar Preview */}\n                <div className={`p-4 border-b ${getNavbarStyles(navbarStyle)}`}>\n                  <div className=\"flex justify-between items-center\">\n                    <div className=\"font-bold text-lg\">Your Brand</div>\n                    <div className=\"flex gap-4\">\n                      <span>Home</span>\n                      <span>About</span>\n                      <span>Services</span>\n                      <span>Contact</span>\n                    </div>\n                  </div>\n                </div>\n\n                {/* Hero Section Preview */}\n                <div className={`p-8 ${getHeroStyles(heroSection)}`}>\n                  {renderHeroContent(heroSection)}\n                </div>\n\n                {/* Content Section */}\n                <div className=\"p-8\">\n                  <div className=\"grid md:grid-cols-3 gap-6\">\n                    {[1, 2, 3].map((i) => (\n                      <div key={i} className=\"text-center\">\n                        <div className=\"w-16 h-16 bg-primary/10 rounded-full mx-auto mb-4 flex items-center justify-center\">\n                          <span className=\"text-2xl\">🎨</span>\n                        </div>\n                        <h3 className=\"font-semibold mb-2\">Feature {i}</h3>\n                        <p className=\"text-sm text-muted-foreground\">\n                          Description of feature {i} goes here.\n                        </p>\n                      </div>\n                    ))}\n                  </div>\n                </div>\n\n                {/* Footer Preview */}\n                <div className={`p-6 border-t mt-8 ${getFooterStyles(footerStyle)}`}>\n                  {renderFooterContent(footerStyle)}\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n        </div>\n      </div>\n\n      {/* Export Options */}\n      <Card>\n        <CardHeader>\n          <CardTitle>Export Options</CardTitle>\n          <CardDescription>\n            Download your customized template\n          </CardDescription>\n        </CardHeader>\n        <CardContent>\n          <div className=\"flex gap-4\">\n            <Button>\n              <Download className=\"h-4 w-4 mr-2\" />\n              Download HTML/CSS\n            </Button>\n            <Button variant=\"outline\">\n              <Download className=\"h-4 w-4 mr-2\" />\n              Download React Components\n            </Button>\n            <Button variant=\"outline\">\n              <Download className=\"h-4 w-4 mr-2\" />\n              Export as PDF\n            </Button>\n          </div>\n        </CardContent>\n      </Card>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AAAA;AAVA;;;;;;;;;;AAgBA,MAAM,eAAe;IACnB;QAAE,IAAI;QAAU,MAAM;QAAU,aAAa;IAA+B;IAC5E;QAAE,IAAI;QAAW,MAAM;QAAW,aAAa;IAA8B;IAC7E;QAAE,IAAI;QAAW,MAAM;QAAW,aAAa;IAA2B;IAC1E;QAAE,IAAI;QAAY,MAAM;QAAY,aAAa;IAA0B;CAC5E;AAED,MAAM,eAAe;IACnB;QAAE,IAAI;QAAS,MAAM;QAAmB,aAAa;IAAmC;IACxF;QAAE,IAAI;QAAS,MAAM;QAAc,aAAa;IAA+B;IAC/E;QAAE,IAAI;QAAS,MAAM;QAAiB,aAAa;IAAoC;IACvF;QAAE,IAAI;QAAS,MAAM;QAAc,aAAa;IAA6B;CAC9E;AAED,MAAM,eAAe;IACnB;QAAE,IAAI;QAAU,MAAM;QAAU,aAAa;IAA4B;IACzE;QAAE,IAAI;QAAY,MAAM;QAAY,aAAa;IAAsB;IACvE;QAAE,IAAI;QAAc,MAAM;QAAc,aAAa;IAAgC;IACrF;QAAE,IAAI;QAAU,MAAM;QAAU,aAAa;IAAiC;CAC/E;AAEc,SAAS;IACtB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAO;IACtC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,MAAM,WAAW,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;IAE5B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,MAAM,YAAY;QAChB,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;QACtD,QAAQ;IACV;IAEA,MAAM,oBAAoB;QACxB,IAAI,CAAC,MAAM;YACT,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,UAAU;QACV,IAAI;YACF,MAAM,SAAS;gBACb;gBACA;gBACA;gBACA,WAAW,IAAI,OAAO,WAAW;YACnC;YAEA,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SACrB,IAAI,CAAC,kBACL,MAAM,CAAC;gBACN,SAAS,KAAK,EAAE;gBAChB,cAAc;gBACd,cAAc;gBACd,cAAc;gBACd;YACF;YAEF,IAAI,OAAO,MAAM;YAEjB,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAChB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,UAAU;QACZ;IACF;IAEA,MAAM,eAAe;QACnB,IAAI,CAAC,MAAM;YACT,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,IAAI;YACF,MAAM,SAAS;gBACb;gBACA;gBACA;gBACA,WAAW,IAAI,OAAO,WAAW;YACnC;YAEA,+BAA+B;YAC/B,MAAM,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,SAChC,IAAI,CAAC,kBACL,MAAM,CAAC;gBACN,SAAS,KAAK,EAAE;gBAChB,cAAc;gBACd,cAAc;gBACd,cAAc;gBACd;YACF;YAEF,IAAI,WAAW,MAAM;YAErB,2BAA2B;YAC3B,MAAM,EAAE,OAAO,YAAY,EAAE,GAAG,MAAM,SACnC,IAAI,CAAC,oBACL,MAAM,CAAC;gBACN,MAAM,KAAK,aAAa,EAAE,aAAa,KAAK,KAAK;gBACjD,OAAO,KAAK,KAAK;gBACjB,SAAS,CAAC;;cAEN,EAAE,aAAa,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,cAAc,KAAK;cACrD,EAAE,aAAa,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,cAAc,KAAK;cACrD,EAAE,aAAa,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,cAAc,KAAK;;wDAEX,CAAC;YACjD;YAEF,IAAI,cAAc,MAAM;YAExB,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAChB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,UAAU;YACxB,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,oBAAoB,CAAC;QACzB,OAAQ;YACN,KAAK;gBACH,qBACE,8OAAC;;sCACC,8OAAC;4BAAG,WAAU;sCAA0B;;;;;;sCACxC,8OAAC;4BAAE,WAAU;sCAAe;;;;;;sCAC5B,8OAAC,kIAAA,CAAA,SAAM;4BAAC,WAAU;sCAA2C;;;;;;;;;;;;YAGnE,KAAK;gBACH,qBACE,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAA0B;;;;;;8CACxC,8OAAC;oCAAE,WAAU;8CAAe;;;;;;8CAC5B,8OAAC,kIAAA,CAAA,SAAM;8CAAC;;;;;;;;;;;;sCAEV,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAK,WAAU;0CAAgB;;;;;;;;;;;;;;;;;YAIxC,KAAK;gBACH,qBACE,8OAAC;;sCACC,8OAAC;4BAAG,WAAU;sCAA0B;;;;;;sCACxC,8OAAC;4BAAE,WAAU;sCAAe;;;;;;sCAC5B,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kIAAA,CAAA,SAAM;8CAAC;;;;;;8CACR,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAQ;8CAAU;;;;;;;;;;;;;;;;;;YAIlC,KAAK;gBACH,qBACE,8OAAC;;sCACC,8OAAC;4BAAG,WAAU;sCAA0B;;;;;;sCACxC,8OAAC;4BAAE,WAAU;sCAAe;;;;;;sCAC5B,8OAAC,kIAAA,CAAA,SAAM;4BAAC,WAAU;sCAAwC;;;;;;;;;;;;YAGhE;gBACE,qBAAO,8OAAC;8BAAI;;;;;;QAChB;IACF;IAEA,MAAM,sBAAsB,CAAC;QAC3B,OAAQ;YACN,KAAK;gBACH,qBACE,8OAAC;8BACC,cAAA,8OAAC;kCAAE;;;;;;;;;;;YAGT,KAAK;gBACH,qBACE,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAqB;;;;;;8CACnC,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;;;;;;;;;;;;;sCAGR,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAqB;;;;;;8CACnC,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;;;;;;;;;;;;;sCAGR,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAqB;;;;;;8CACnC,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;;;;;;;;;;;;;sCAGR,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAqB;;;;;;8CACnC,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;;;;;;;;;;;;;;;;;;;YAKd,KAAK;gBACH,qBACE,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAqB;;;;;;sCACnC,8OAAC;4BAAE,WAAU;sCAAe;;;;;;sCAC5B,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,MAAK;oCACL,aAAY;oCACZ,WAAU;;;;;;8CAEZ,8OAAC,kIAAA,CAAA,SAAM;8CAAC;;;;;;;;;;;;;;;;;;YAIhB,KAAK;gBACH,qBACE,8OAAC;;sCACC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;8CAAK;;;;;;8CACN,8OAAC;8CAAK;;;;;;8CACN,8OAAC;8CAAK;;;;;;8CACN,8OAAC;8CAAK;;;;;;;;;;;;sCAER,8OAAC;sCAAE;;;;;;;;;;;;YAGT;gBACE,qBAAO,8OAAC;8BAAI;;;;;;QAChB;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;0CAAoC;;;;;;0CAClD,8OAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;kCAIvC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,kIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAU,MAAK;gCAAK,SAAS;gCAAmB,UAAU,UAAU,CAAC;;kDACnF,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCACf,SAAS,cAAc;;;;;;;0CAE1B,8OAAC,kIAAA,CAAA,SAAM;gCAAC,MAAK;gCAAK,SAAS;gCAAc,UAAU,CAAC;;kDAClD,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;0BAMvC,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,gIAAA,CAAA,OAAI;;8CACH,8OAAC,gIAAA,CAAA,aAAU;;sDACT,8OAAC,gIAAA,CAAA,YAAS;4CAAC,WAAU;sDAAU;;;;;;sDAC/B,8OAAC,gIAAA,CAAA,kBAAe;sDAAC;;;;;;;;;;;;8CAInB,8OAAC,gIAAA,CAAA,cAAW;oCAAC,WAAU;;sDAErB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,iIAAA,CAAA,QAAK;8DAAC;;;;;;8DACP,8OAAC,kIAAA,CAAA,SAAM;oDAAC,OAAO;oDAAa,eAAe;;sEACzC,8OAAC,kIAAA,CAAA,gBAAa;sEACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;gEAAC,aAAY;;;;;;;;;;;sEAE3B,8OAAC,kIAAA,CAAA,gBAAa;sEACX,aAAa,GAAG,CAAC,CAAC,sBACjB,8OAAC,kIAAA,CAAA,aAAU;oEAAgB,OAAO,MAAM,EAAE;8EACxC,cAAA,8OAAC;;0FACC,8OAAC;gFAAI,WAAU;0FAAe,MAAM,IAAI;;;;;;0FACxC,8OAAC;gFAAI,WAAU;0FAAiC,MAAM,WAAW;;;;;;;;;;;;mEAHpD,MAAM,EAAE;;;;;;;;;;;;;;;;;;;;;;sDAYjC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,iIAAA,CAAA,QAAK;8DAAC;;;;;;8DACP,8OAAC,kIAAA,CAAA,SAAM;oDAAC,OAAO;oDAAa,eAAe;;sEACzC,8OAAC,kIAAA,CAAA,gBAAa;sEACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;gEAAC,aAAY;;;;;;;;;;;sEAE3B,8OAAC,kIAAA,CAAA,gBAAa;sEACX,aAAa,GAAG,CAAC,CAAC,qBACjB,8OAAC,kIAAA,CAAA,aAAU;oEAAe,OAAO,KAAK,EAAE;8EACtC,cAAA,8OAAC;;0FACC,8OAAC;gFAAI,WAAU;0FAAe,KAAK,IAAI;;;;;;0FACvC,8OAAC;gFAAI,WAAU;0FAAiC,KAAK,WAAW;;;;;;;;;;;;mEAHnD,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;;sDAYhC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,iIAAA,CAAA,QAAK;8DAAC;;;;;;8DACP,8OAAC,kIAAA,CAAA,SAAM;oDAAC,OAAO;oDAAa,eAAe;;sEACzC,8OAAC,kIAAA,CAAA,gBAAa;sEACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;gEAAC,aAAY;;;;;;;;;;;sEAE3B,8OAAC,kIAAA,CAAA,gBAAa;sEACX,aAAa,GAAG,CAAC,CAAC,uBACjB,8OAAC,kIAAA,CAAA,aAAU;oEAAiB,OAAO,OAAO,EAAE;8EAC1C,cAAA,8OAAC;;0FACC,8OAAC;gFAAI,WAAU;0FAAe,OAAO,IAAI;;;;;;0FACzC,8OAAC;gFAAI,WAAU;0FAAiC,OAAO,WAAW;;;;;;;;;;;;mEAHrD,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;;wCAWjC,CAAC,sBACA,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAE,WAAU;0DAAgC;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAUvD,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,gIAAA,CAAA,OAAI;;8CACH,8OAAC,gIAAA,CAAA,aAAU;;sDACT,8OAAC,gIAAA,CAAA,YAAS;4CAAC,WAAU;sDAAU;;;;;;sDAC/B,8OAAC,gIAAA,CAAA,kBAAe;sDAAC;;;;;;;;;;;;8CAInB,8OAAC,gIAAA,CAAA,cAAW;8CACV,cAAA,8OAAC;wCAAI,WAAU;;0DAEb,8OAAC;gDAAI,WAAW,CAAC,aAAa,EAAE,gBAAgB,cAAc;0DAC5D,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEAAoB;;;;;;sEACnC,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;8EAAK;;;;;;8EACN,8OAAC;8EAAK;;;;;;8EACN,8OAAC;8EAAK;;;;;;8EACN,8OAAC;8EAAK;;;;;;;;;;;;;;;;;;;;;;;0DAMZ,8OAAC;gDAAI,WAAW,CAAC,IAAI,EAAE,cAAc,cAAc;0DAChD,kBAAkB;;;;;;0DAIrB,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;8DACZ;wDAAC;wDAAG;wDAAG;qDAAE,CAAC,GAAG,CAAC,CAAC,kBACd,8OAAC;4DAAY,WAAU;;8EACrB,8OAAC;oEAAI,WAAU;8EACb,cAAA,8OAAC;wEAAK,WAAU;kFAAW;;;;;;;;;;;8EAE7B,8OAAC;oEAAG,WAAU;;wEAAqB;wEAAS;;;;;;;8EAC5C,8OAAC;oEAAE,WAAU;;wEAAgC;wEACnB;wEAAE;;;;;;;;2DANpB;;;;;;;;;;;;;;;0DAchB,8OAAC;gDAAI,WAAW,CAAC,kBAAkB,EAAE,gBAAgB,cAAc;0DAChE,oBAAoB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASjC,8OAAC,gIAAA,CAAA,OAAI;;kCACH,8OAAC,gIAAA,CAAA,aAAU;;0CACT,8OAAC,gIAAA,CAAA,YAAS;0CAAC;;;;;;0CACX,8OAAC,gIAAA,CAAA,kBAAe;0CAAC;;;;;;;;;;;;kCAInB,8OAAC,gIAAA,CAAA,cAAW;kCACV,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kIAAA,CAAA,SAAM;;sDACL,8OAAC;4CAAS,WAAU;;;;;;wCAAiB;;;;;;;8CAGvC,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAQ;;sDACd,8OAAC;4CAAS,WAAU;;;;;;wCAAiB;;;;;;;8CAGvC,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAQ;;sDACd,8OAAC;4CAAS,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQnD", "debugId": null}}]}