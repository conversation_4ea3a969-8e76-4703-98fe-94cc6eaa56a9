{"version": 3, "sources": [], "sections": [{"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/lib/supabase/middleware.ts"], "sourcesContent": ["import { createServerClient } from '@supabase/ssr'\nimport { NextResponse, type NextRequest } from 'next/server'\n\nexport async function updateSession(request: NextRequest) {\n  let supabaseResponse = NextResponse.next({\n    request,\n  })\n\n  const supabase = createServerClient(\n    process.env.NEXT_PUBLIC_SUPABASE_URL!,\n    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,\n    {\n      cookies: {\n        getAll() {\n          return request.cookies.getAll()\n        },\n        setAll(cookiesToSet) {\n          cookiesToSet.forEach(({ name, value, options }) => request.cookies.set(name, value))\n          supabaseResponse = NextResponse.next({\n            request,\n          })\n          cookiesToSet.forEach(({ name, value, options }) =>\n            supabaseResponse.cookies.set(name, value, options)\n          )\n        },\n      },\n    }\n  )\n\n  // IMPORTANT: Avoid writing any logic between createServerClient and\n  // supabase.auth.getUser(). A simple mistake could make it very hard to debug\n  // issues with users being randomly logged out.\n\n  const {\n    data: { user },\n  } = await supabase.auth.getUser()\n\n  if (\n    !user &&\n    !request.nextUrl.pathname.startsWith('/login') &&\n    !request.nextUrl.pathname.startsWith('/auth')\n  ) {\n    // no user, potentially respond by redirecting the user to the login page\n    const url = request.nextUrl.clone()\n    url.pathname = '/login'\n    return NextResponse.redirect(url)\n  }\n\n  // IMPORTANT: You *must* return the supabaseResponse object as it is. If you're\n  // creating a new response object with NextResponse.next() make sure to:\n  // 1. Pass the request in it, like so:\n  //    const myNewResponse = NextResponse.next({ request })\n  // 2. Copy over the cookies, like so:\n  //    myNewResponse.cookies.setAll(supabaseResponse.cookies.getAll())\n  // 3. Change the myNewResponse object to fit your needs, but avoid changing\n  //    the cookies!\n  // 4. Finally:\n  //    return myNewResponse\n  // If this is not done, you may be causing the browser and server to go out\n  // of sync and terminate the user's session prematurely!\n\n  return supabaseResponse\n}\n"], "names": [], "mappings": ";;;AAAA;AAAA;AACA;AAAA;;;AAEO,eAAe,cAAc,OAAoB;IACtD,IAAI,mBAAmB,6LAAA,CAAA,eAAY,CAAC,IAAI,CAAC;QACvC;IACF;IAEA,MAAM,WAAW,CAAA,GAAA,iLAAA,CAAA,qBAAkB,AAAD,EAChC,QAAQ,GAAG,CAAC,wBAAwB,EACpC,QAAQ,GAAG,CAAC,6BAA6B,EACzC;QACE,SAAS;YACP;gBACE,OAAO,QAAQ,OAAO,CAAC,MAAM;YAC/B;YACA,QAAO,YAAY;gBACjB,aAAa,OAAO,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,GAAK,QAAQ,OAAO,CAAC,GAAG,CAAC,MAAM;gBAC7E,mBAAmB,6LAAA,CAAA,eAAY,CAAC,IAAI,CAAC;oBACnC;gBACF;gBACA,aAAa,OAAO,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,GAC5C,iBAAiB,OAAO,CAAC,GAAG,CAAC,MAAM,OAAO;YAE9C;QACF;IACF;IAGF,oEAAoE;IACpE,6EAA6E;IAC7E,+CAA+C;IAE/C,MAAM,EACJ,MAAM,EAAE,IAAI,EAAE,EACf,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;IAE/B,IACE,CAAC,QACD,CAAC,QAAQ,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,aACrC,CAAC,QAAQ,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,UACrC;QACA,yEAAyE;QACzE,MAAM,MAAM,QAAQ,OAAO,CAAC,KAAK;QACjC,IAAI,QAAQ,GAAG;QACf,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC;IAC/B;IAEA,+EAA+E;IAC/E,wEAAwE;IACxE,sCAAsC;IACtC,0DAA0D;IAC1D,qCAAqC;IACrC,qEAAqE;IACrE,2EAA2E;IAC3E,kBAAkB;IAClB,cAAc;IACd,0BAA0B;IAC1B,2EAA2E;IAC3E,wDAAwD;IAExD,OAAO;AACT"}}, {"offset": {"line": 80, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/middleware.ts"], "sourcesContent": ["import { type NextRequest } from 'next/server'\nimport { updateSession } from '@/lib/supabase/middleware'\n\nexport async function middleware(request: NextRequest) {\n  return await updateSession(request)\n}\n\nexport const config = {\n  matcher: [\n    /*\n     * Match all request paths except for the ones starting with:\n     * - _next/static (static files)\n     * - _next/image (image optimization files)\n     * - favicon.ico (favicon file)\n     * Feel free to modify this pattern to include more paths.\n     */\n    '/((?!_next/static|_next/image|favicon.ico|.*\\\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)',\n  ],\n}\n"], "names": [], "mappings": ";;;;AACA;;AAEO,eAAe,WAAW,OAAoB;IACnD,OAAO,MAAM,CAAA,GAAA,4IAAA,CAAA,gBAAa,AAAD,EAAE;AAC7B;AAEO,MAAM,SAAS;IACpB,SAAS;QACP;;;;;;KAMC,GACD;KACD;AACH"}}]}