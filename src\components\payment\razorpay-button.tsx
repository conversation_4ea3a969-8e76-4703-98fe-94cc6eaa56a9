"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { razorpayConfig, type RazorpayOptions, type RazorpayResponse } from "@/lib/razorpay"

interface RazorpayButtonProps {
  amount: number
  currency?: string
  description?: string
  templateId?: string
  onSuccess?: (response: RazorpayResponse) => void
  onError?: (error: any) => void
  disabled?: boolean
  children?: React.ReactNode
}

declare global {
  interface Window {
    Razorpay: any
  }
}

export function RazorpayButton({
  amount,
  currency = "INR",
  description = "Payment",
  templateId,
  onSuccess,
  onError,
  disabled = false,
  children = "Pay Now",
}: RazorpayButtonProps) {
  const [loading, setLoading] = useState(false)

  const handlePayment = async () => {
    try {
      setLoading(true)

      // Create order on server
      const orderResponse = await fetch("/api/payments/create-order", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          amount,
          currency,
          receipt: `receipt_${Date.now()}`,
          templateId,
        }),
      })

      if (!orderResponse.ok) {
        throw new Error("Failed to create order")
      }

      const orderData = await orderResponse.json()

      // Load Razorpay script if not already loaded
      if (!window.Razorpay) {
        const script = document.createElement("script")
        script.src = "https://checkout.razorpay.com/v1/checkout.js"
        script.async = true
        document.body.appendChild(script)
        
        await new Promise((resolve) => {
          script.onload = resolve
        })
      }

      // Configure Razorpay options
      const options: RazorpayOptions = {
        key: razorpayConfig.key,
        amount: orderData.amount,
        currency: orderData.currency,
        name: "KaleidoneX",
        description,
        order_id: orderData.orderId,
        handler: async (response: RazorpayResponse) => {
          try {
            // Verify payment on server
            const verifyResponse = await fetch("/api/payments/verify", {
              method: "POST",
              headers: {
                "Content-Type": "application/json",
              },
              body: JSON.stringify({
                ...response,
                templateId
              }),
            })

            if (verifyResponse.ok) {
              onSuccess?.(response)
            } else {
              throw new Error("Payment verification failed")
            }
          } catch (error) {
            onError?.(error)
          }
        },
        prefill: {
          name: "",
          email: "",
          contact: "",
        },
        theme: {
          color: "#3B82F6",
        },
      }

      // Open Razorpay checkout
      const razorpay = new window.Razorpay(options)
      razorpay.open()

      razorpay.on("payment.failed", (response: any) => {
        onError?.(response.error)
      })
    } catch (error) {
      onError?.(error)
    } finally {
      setLoading(false)
    }
  }

  return (
    <Button
      onClick={handlePayment}
      disabled={disabled || loading}
      className="w-full"
    >
      {loading ? "Processing..." : children}
    </Button>
  )
}
