{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_061ca3cf._.js", "server/edge/chunks/[root-of-the-server]__de110ef0._.js", "server/edge/chunks/edge-wrapper_452e0654.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next\\/static|_next\\/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "903ou+si8jPmiJ9R1U8PjxHzUt1mwAm+hUYuJyHsngs=", "__NEXT_PREVIEW_MODE_ID": "76a9aa58e5e0b7b6bb3a7551d868e38a", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "e0724bb6654de0d4e0210a05445cb2f6c3b0be191a62dfb8f32c612335395907", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "215eefc1feb82ca160707cdc9e1fbeea666ef8202d540010af8a65a2731761bc"}}}, "sortedMiddleware": ["/"], "functions": {}}