{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_061ca3cf._.js", "server/edge/chunks/[root-of-the-server]__de110ef0._.js", "server/edge/chunks/edge-wrapper_452e0654.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next\\/static|_next\\/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "903ou+si8jPmiJ9R1U8PjxHzUt1mwAm+hUYuJyHsngs=", "__NEXT_PREVIEW_MODE_ID": "c20f043090b02917c819967e0d67547a", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "c338fcac110371ef6b7cdabc3f0855c34e366dd42036d2faa457c543abbb49dc", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "78a730b346e1d2f07bdf971cd5875048e8c57d711ebb0a5c2581c29232899922"}}}, "sortedMiddleware": ["/"], "functions": {}}