const CHUNK_PUBLIC_PATH = "server/app/api/create-demo-users/route.js";
const runtime = require("../../../chunks/[turbopack]_runtime.js");
runtime.loadChunk("server/chunks/node_modules_38b0f328._.js");
runtime.loadChunk("server/chunks/node_modules_next_397caf73._.js");
runtime.loadChunk("server/chunks/node_modules_tr46_816df9d9._.js");
runtime.loadChunk("server/chunks/node_modules_@supabase_auth-js_dist_module_fbd19a5c._.js");
runtime.loadChunk("server/chunks/node_modules_2eccb26d._.js");
runtime.loadChunk("server/chunks/[root-of-the-server]__664cdf6f._.js");
runtime.getOrInstantiateRuntimeModule("[project]/.next-internal/server/app/api/create-demo-users/route/actions.js [app-rsc] (server actions loader, ecmascript)", CHUNK_PUBLIC_PATH);
runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/src/app/api/create-demo-users/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)", CHUNK_PUBLIC_PATH);
module.exports = runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/src/app/api/create-demo-users/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)", CHUNK_PUBLIC_PATH).exports;
