import { Card, CardContent, CardDescription, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"

const orders = [
  {
    id: "ORD-001",
    date: "2024-01-15",
    amount: "₹2,499",
    status: "completed",
    items: "Premium Plan - 1 Month"
  },
  {
    id: "ORD-002", 
    date: "2024-01-10",
    amount: "₹999",
    status: "pending",
    items: "Basic Plan - 1 Month"
  },
  {
    id: "ORD-003",
    date: "2024-01-05",
    amount: "₹4,999",
    status: "completed",
    items: "Enterprise Plan - 1 Month"
  }
]

export default function OrdersPage() {
  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Orders</h1>
          <p className="text-muted-foreground">
            View and manage your order history
          </p>
        </div>
        <Button>Create New Order</Button>
      </div>

      <div className="space-y-4">
        {orders.map((order) => (
          <Card key={order.id}>
            <CardHeader>
              <div className="flex justify-between items-start">
                <div>
                  <CardTitle className="text-lg">{order.id}</CardTitle>
                  <CardDescription>{order.date}</CardDescription>
                </div>
                <Badge 
                  variant={order.status === 'completed' ? 'default' : 'secondary'}
                >
                  {order.status}
                </Badge>
              </div>
            </CardHeader>
            <CardContent>
              <div className="flex justify-between items-center">
                <div>
                  <p className="font-medium">{order.items}</p>
                  <p className="text-2xl font-bold text-green-600">{order.amount}</p>
                </div>
                <div className="space-x-2">
                  <Button variant="outline" size="sm">View Details</Button>
                  {order.status === 'pending' && (
                    <Button size="sm">Complete Payment</Button>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  )
}
