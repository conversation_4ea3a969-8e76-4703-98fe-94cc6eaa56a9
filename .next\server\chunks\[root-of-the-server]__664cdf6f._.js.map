{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 108, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Documents/kaleidonex/src/app/api/create-demo-users/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { createClient } from '@supabase/supabase-js'\n\nexport async function POST(request: NextRequest) {\n  try {\n    // Use service role key for admin operations\n    const supabase = createClient(\n      process.env.NEXT_PUBLIC_SUPABASE_URL!,\n      process.env.SUPABASE_SERVICE_ROLE_KEY!,\n      {\n        auth: {\n          autoRefreshToken: false,\n          persistSession: false\n        }\n      }\n    )\n\n    const demoUsers = [\n      {\n        email: '<EMAIL>',\n        password: 'admin123',\n        role: 'admin',\n        full_name: 'Admin Demo'\n      },\n      {\n        email: '<EMAIL>',\n        password: 'user123',\n        role: 'user',\n        full_name: 'User Demo'\n      }\n    ]\n\n    const results = []\n\n    for (const user of demoUsers) {\n      try {\n        // Try to create the user (will fail if already exists)\n        const { data: newUser, error: createError } = await supabase.auth.admin.createUser({\n          email: user.email,\n          password: user.password,\n          email_confirm: true,\n          user_metadata: {\n            full_name: user.full_name\n          }\n        })\n\n        if (createError) {\n          if (createError.message.includes('already registered')) {\n            // User already exists, try to find and update profile\n            const { data: users, error: listError } = await supabase.auth.admin.listUsers()\n\n            if (!listError && users) {\n              const existingUser = users.users.find(u => u.email === user.email)\n\n              if (existingUser) {\n                // Update profile\n                const { error: profileError } = await supabase\n                  .from('profiles')\n                  .upsert({\n                    id: existingUser.id,\n                    full_name: user.full_name,\n                    role: user.role,\n                    updated_at: new Date().toISOString()\n                  })\n\n                if (profileError) {\n                  console.error(`Profile update error for ${user.email}:`, profileError)\n                }\n\n                results.push({\n                  email: user.email,\n                  status: 'updated',\n                  role: user.role\n                })\n              } else {\n                results.push({\n                  email: user.email,\n                  status: 'error',\n                  error: 'User exists but could not be found'\n                })\n              }\n            } else {\n              results.push({\n                email: user.email,\n                status: 'error',\n                error: 'Could not list users'\n              })\n            }\n          } else {\n            console.error(`User creation error for ${user.email}:`, createError)\n            results.push({\n              email: user.email,\n              status: 'error',\n              error: createError.message\n            })\n          }\n        } else if (newUser.user) {\n          // User created successfully, create profile\n          const { error: profileError } = await supabase\n            .from('profiles')\n            .insert({\n              id: newUser.user.id,\n              full_name: user.full_name,\n              role: user.role\n            })\n\n          if (profileError) {\n            console.error(`Profile creation error for ${user.email}:`, profileError)\n          }\n\n          results.push({\n            email: user.email,\n            status: 'created',\n            role: user.role\n          })\n        }\n      } catch (error: any) {\n        console.error(`Error processing user ${user.email}:`, error)\n        results.push({\n          email: user.email,\n          status: 'error',\n          error: error.message\n        })\n      }\n    }\n\n    return NextResponse.json({\n      success: true,\n      message: 'Demo users processed successfully',\n      results\n    })\n\n  } catch (error: any) {\n    console.error('Demo user creation error:', error)\n    return NextResponse.json(\n      {\n        success: false,\n        error: error.message || 'Failed to create demo users'\n      },\n      { status: 500 }\n    )\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,4CAA4C;QAC5C,MAAM,WAAW,CAAA,GAAA,yLAAA,CAAA,eAAY,AAAD,gFAE1B,QAAQ,GAAG,CAAC,yBAAyB,EACrC;YACE,MAAM;gBACJ,kBAAkB;gBAClB,gBAAgB;YAClB;QACF;QAGF,MAAM,YAAY;YAChB;gBACE,OAAO;gBACP,UAAU;gBACV,MAAM;gBACN,WAAW;YACb;YACA;gBACE,OAAO;gBACP,UAAU;gBACV,MAAM;gBACN,WAAW;YACb;SACD;QAED,MAAM,UAAU,EAAE;QAElB,KAAK,MAAM,QAAQ,UAAW;YAC5B,IAAI;gBACF,uDAAuD;gBACvD,MAAM,EAAE,MAAM,OAAO,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC;oBACjF,OAAO,KAAK,KAAK;oBACjB,UAAU,KAAK,QAAQ;oBACvB,eAAe;oBACf,eAAe;wBACb,WAAW,KAAK,SAAS;oBAC3B;gBACF;gBAEA,IAAI,aAAa;oBACf,IAAI,YAAY,OAAO,CAAC,QAAQ,CAAC,uBAAuB;wBACtD,sDAAsD;wBACtD,MAAM,EAAE,MAAM,KAAK,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,KAAK,CAAC,SAAS;wBAE7E,IAAI,CAAC,aAAa,OAAO;4BACvB,MAAM,eAAe,MAAM,KAAK,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,KAAK,KAAK,KAAK,KAAK;4BAEjE,IAAI,cAAc;gCAChB,iBAAiB;gCACjB,MAAM,EAAE,OAAO,YAAY,EAAE,GAAG,MAAM,SACnC,IAAI,CAAC,YACL,MAAM,CAAC;oCACN,IAAI,aAAa,EAAE;oCACnB,WAAW,KAAK,SAAS;oCACzB,MAAM,KAAK,IAAI;oCACf,YAAY,IAAI,OAAO,WAAW;gCACpC;gCAEF,IAAI,cAAc;oCAChB,QAAQ,KAAK,CAAC,CAAC,yBAAyB,EAAE,KAAK,KAAK,CAAC,CAAC,CAAC,EAAE;gCAC3D;gCAEA,QAAQ,IAAI,CAAC;oCACX,OAAO,KAAK,KAAK;oCACjB,QAAQ;oCACR,MAAM,KAAK,IAAI;gCACjB;4BACF,OAAO;gCACL,QAAQ,IAAI,CAAC;oCACX,OAAO,KAAK,KAAK;oCACjB,QAAQ;oCACR,OAAO;gCACT;4BACF;wBACF,OAAO;4BACL,QAAQ,IAAI,CAAC;gCACX,OAAO,KAAK,KAAK;gCACjB,QAAQ;gCACR,OAAO;4BACT;wBACF;oBACF,OAAO;wBACL,QAAQ,KAAK,CAAC,CAAC,wBAAwB,EAAE,KAAK,KAAK,CAAC,CAAC,CAAC,EAAE;wBACxD,QAAQ,IAAI,CAAC;4BACX,OAAO,KAAK,KAAK;4BACjB,QAAQ;4BACR,OAAO,YAAY,OAAO;wBAC5B;oBACF;gBACF,OAAO,IAAI,QAAQ,IAAI,EAAE;oBACvB,4CAA4C;oBAC5C,MAAM,EAAE,OAAO,YAAY,EAAE,GAAG,MAAM,SACnC,IAAI,CAAC,YACL,MAAM,CAAC;wBACN,IAAI,QAAQ,IAAI,CAAC,EAAE;wBACnB,WAAW,KAAK,SAAS;wBACzB,MAAM,KAAK,IAAI;oBACjB;oBAEF,IAAI,cAAc;wBAChB,QAAQ,KAAK,CAAC,CAAC,2BAA2B,EAAE,KAAK,KAAK,CAAC,CAAC,CAAC,EAAE;oBAC7D;oBAEA,QAAQ,IAAI,CAAC;wBACX,OAAO,KAAK,KAAK;wBACjB,QAAQ;wBACR,MAAM,KAAK,IAAI;oBACjB;gBACF;YACF,EAAE,OAAO,OAAY;gBACnB,QAAQ,KAAK,CAAC,CAAC,sBAAsB,EAAE,KAAK,KAAK,CAAC,CAAC,CAAC,EAAE;gBACtD,QAAQ,IAAI,CAAC;oBACX,OAAO,KAAK,KAAK;oBACjB,QAAQ;oBACR,OAAO,MAAM,OAAO;gBACtB;YACF;QACF;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,SAAS;YACT;QACF;IAEF,EAAE,OAAO,OAAY;QACnB,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YACE,SAAS;YACT,OAAO,MAAM,OAAO,IAAI;QAC1B,GACA;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}