{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Documents/kaleidonex/src/components/layout/main-layout.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const MainLayout = registerClientReference(\n    function() { throw new Error(\"Attempted to call MainLayout() from the server but MainLayout is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/main-layout.tsx <module evaluation>\",\n    \"MainLayout\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,aAAa,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,uEACA", "debugId": null}}, {"offset": {"line": 21, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Documents/kaleidonex/src/components/layout/main-layout.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const MainLayout = registerClientReference(\n    function() { throw new Error(\"Attempted to call MainLayout() from the server but MainLayout is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/main-layout.tsx\",\n    \"MainLayout\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,aAAa,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,mDACA", "debugId": null}}, {"offset": {"line": 35, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 45, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Documents/kaleidonex/src/app/%28dashboard%29/layout.tsx"], "sourcesContent": ["import { MainLayout } from \"@/components/layout/main-layout\"\n\nexport default function DashboardLayout({\n  children,\n}: {\n  children: React.ReactNode\n}) {\n  return <MainLayout>{children}</MainLayout>\n}\n"], "names": [], "mappings": ";;;;AAAA;;;AAEe,SAAS,gBAAgB,EACtC,QAAQ,EAGT;IACC,qBAAO,8OAAC,8IAAA,CAAA,aAAU;kBAAE;;;;;;AACtB", "debugId": null}}]}