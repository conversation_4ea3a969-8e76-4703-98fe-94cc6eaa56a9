{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Documents/kaleidonex/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 104, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Documents/kaleidonex/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 130, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Documents/kaleidonex/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Label({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  return (\n    <LabelPrimitive.Root\n      data-slot=\"label\"\n      className={cn(\n        \"flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,EACb,SAAS,EACT,GAAG,OAC8C;IACjD,qBACE,8OAAC,iKAAA,CAAA,OAAmB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uNACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 158, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Documents/kaleidonex/src/components/ui/tabs.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as TabsPrimitive from \"@radix-ui/react-tabs\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Tabs({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.Root>) {\n  return (\n    <TabsPrimitive.Root\n      data-slot=\"tabs\"\n      className={cn(\"flex flex-col gap-2\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction TabsList({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.List>) {\n  return (\n    <TabsPrimitive.List\n      data-slot=\"tabs-list\"\n      className={cn(\n        \"bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-[3px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TabsTrigger({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.Trigger>) {\n  return (\n    <TabsPrimitive.Trigger\n      data-slot=\"tabs-trigger\"\n      className={cn(\n        \"data-[state=active]:bg-background dark:data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 text-foreground dark:text-muted-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TabsContent({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.Content>) {\n  return (\n    <TabsPrimitive.Content\n      data-slot=\"tabs-content\"\n      className={cn(\"flex-1 outline-none\", className)}\n      {...props}\n    />\n  )\n}\n\nexport { Tabs, TabsList, TabsTrigger, TabsContent }\n"], "names": [], "mappings": ";;;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,KAAK,EACZ,SAAS,EACT,GAAG,OAC6C;IAChD,qBACE,8OAAC,gKAAA,CAAA,OAAkB;QACjB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,uBAAuB;QACpC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,SAAS,EAChB,SAAS,EACT,GAAG,OAC6C;IAChD,qBACE,8OAAC,gKAAA,CAAA,OAAkB;QACjB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uGACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,gKAAA,CAAA,UAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mqBACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,gKAAA,CAAA,UAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,uBAAuB;QACpC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 222, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Documents/kaleidonex/src/components/ui/slider.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SliderPrimitive from \"@radix-ui/react-slider\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Slider({\n  className,\n  defaultValue,\n  value,\n  min = 0,\n  max = 100,\n  ...props\n}: React.ComponentProps<typeof SliderPrimitive.Root>) {\n  const _values = React.useMemo(\n    () =>\n      Array.isArray(value)\n        ? value\n        : Array.isArray(defaultValue)\n          ? defaultValue\n          : [min, max],\n    [value, defaultValue, min, max]\n  )\n\n  return (\n    <SliderPrimitive.Root\n      data-slot=\"slider\"\n      defaultValue={defaultValue}\n      value={value}\n      min={min}\n      max={max}\n      className={cn(\n        \"relative flex w-full touch-none items-center select-none data-[disabled]:opacity-50 data-[orientation=vertical]:h-full data-[orientation=vertical]:min-h-44 data-[orientation=vertical]:w-auto data-[orientation=vertical]:flex-col\",\n        className\n      )}\n      {...props}\n    >\n      <SliderPrimitive.Track\n        data-slot=\"slider-track\"\n        className={cn(\n          \"bg-muted relative grow overflow-hidden rounded-full data-[orientation=horizontal]:h-1.5 data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-1.5\"\n        )}\n      >\n        <SliderPrimitive.Range\n          data-slot=\"slider-range\"\n          className={cn(\n            \"bg-primary absolute data-[orientation=horizontal]:h-full data-[orientation=vertical]:w-full\"\n          )}\n        />\n      </SliderPrimitive.Track>\n      {Array.from({ length: _values.length }, (_, index) => (\n        <SliderPrimitive.Thumb\n          data-slot=\"slider-thumb\"\n          key={index}\n          className=\"border-primary bg-background ring-ring/50 block size-4 shrink-0 rounded-full border shadow-sm transition-[color,box-shadow] hover:ring-4 focus-visible:ring-4 focus-visible:outline-hidden disabled:pointer-events-none disabled:opacity-50\"\n        />\n      ))}\n    </SliderPrimitive.Root>\n  )\n}\n\nexport { Slider }\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,SAAS,OAAO,EACd,SAAS,EACT,YAAY,EACZ,KAAK,EACL,MAAM,CAAC,EACP,MAAM,GAAG,EACT,GAAG,OAC+C;IAClD,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,UAAa,AAAD,EAC1B,IACE,MAAM,OAAO,CAAC,SACV,QACA,MAAM,OAAO,CAAC,gBACZ,eACA;YAAC;YAAK;SAAI,EAClB;QAAC;QAAO;QAAc;QAAK;KAAI;IAGjC,qBACE,8OAAC,kKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,cAAc;QACd,OAAO;QACP,KAAK;QACL,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uOACA;QAED,GAAG,KAAK;;0BAET,8OAAC,kKAAA,CAAA,QAAqB;gBACpB,aAAU;gBACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV;0BAGF,cAAA,8OAAC,kKAAA,CAAA,QAAqB;oBACpB,aAAU;oBACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV;;;;;;;;;;;YAIL,MAAM,IAAI,CAAC;gBAAE,QAAQ,QAAQ,MAAM;YAAC,GAAG,CAAC,GAAG,sBAC1C,8OAAC,kKAAA,CAAA,QAAqB;oBACpB,aAAU;oBAEV,WAAU;mBADL;;;;;;;;;;;AAMf", "debugId": null}}, {"offset": {"line": 293, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Documents/kaleidonex/src/app/customize/page.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState } from \"react\"\nimport { <PERSON>, Card<PERSON>ontent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\"\nimport { Button } from \"@/components/ui/button\"\nimport { Input } from \"@/components/ui/input\"\nimport { Label } from \"@/components/ui/label\"\nimport { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>List, Ta<PERSON>Trigger } from \"@/components/ui/tabs\"\nimport { Slider } from \"@/components/ui/slider\"\nimport { \n  Palette, \n  Type, \n  Layout, \n  Image as ImageIcon, \n  Save, \n  Download,\n  Eye,\n  Undo,\n  Redo\n} from \"lucide-react\"\n\nexport default function CustomizePage() {\n  const [selectedColor, setSelectedColor] = useState(\"#3B82F6\")\n  const [fontSize, setFontSize] = useState([16])\n  const [spacing, setSpacing] = useState([8])\n\n  const colorPresets = [\n    \"#3B82F6\", \"#EF4444\", \"#10B981\", \"#F59E0B\", \n    \"#8B5CF6\", \"#EC4899\", \"#06B6D4\", \"#84CC16\"\n  ]\n\n  return (\n    <div className=\"space-y-6\">\n      <div className=\"flex justify-between items-center\">\n        <div>\n          <h1 className=\"text-3xl font-bold tracking-tight\">Live Preview Customizer</h1>\n          <p className=\"text-muted-foreground\">\n            Customize your template in real-time with our visual editor\n          </p>\n        </div>\n        <div className=\"flex gap-2\">\n          <Button variant=\"outline\" size=\"sm\">\n            <Undo className=\"h-4 w-4 mr-2\" />\n            Undo\n          </Button>\n          <Button variant=\"outline\" size=\"sm\">\n            <Redo className=\"h-4 w-4 mr-2\" />\n            Redo\n          </Button>\n          <Button variant=\"outline\" size=\"sm\">\n            <Eye className=\"h-4 w-4 mr-2\" />\n            Preview\n          </Button>\n          <Button size=\"sm\">\n            <Save className=\"h-4 w-4 mr-2\" />\n            Save\n          </Button>\n        </div>\n      </div>\n\n      <div className=\"grid grid-cols-1 lg:grid-cols-4 gap-6\">\n        {/* Customization Panel */}\n        <div className=\"lg:col-span-1\">\n          <Card>\n            <CardHeader>\n              <CardTitle className=\"text-lg\">Customization Panel</CardTitle>\n              <CardDescription>\n                Adjust your template settings\n              </CardDescription>\n            </CardHeader>\n            <CardContent>\n              <Tabs defaultValue=\"colors\" className=\"w-full\">\n                <TabsList className=\"grid w-full grid-cols-4\">\n                  <TabsTrigger value=\"colors\" className=\"p-2\">\n                    <Palette className=\"h-4 w-4\" />\n                  </TabsTrigger>\n                  <TabsTrigger value=\"typography\" className=\"p-2\">\n                    <Type className=\"h-4 w-4\" />\n                  </TabsTrigger>\n                  <TabsTrigger value=\"layout\" className=\"p-2\">\n                    <Layout className=\"h-4 w-4\" />\n                  </TabsTrigger>\n                  <TabsTrigger value=\"media\" className=\"p-2\">\n                    <ImageIcon className=\"h-4 w-4\" />\n                  </TabsTrigger>\n                </TabsList>\n\n                <TabsContent value=\"colors\" className=\"space-y-4\">\n                  <div>\n                    <Label>Primary Color</Label>\n                    <div className=\"flex gap-2 mt-2\">\n                      {colorPresets.map((color) => (\n                        <button\n                          key={color}\n                          className=\"w-8 h-8 rounded-full border-2 border-white shadow-md\"\n                          style={{ backgroundColor: color }}\n                          onClick={() => setSelectedColor(color)}\n                        />\n                      ))}\n                    </div>\n                    <Input\n                      type=\"color\"\n                      value={selectedColor}\n                      onChange={(e) => setSelectedColor(e.target.value)}\n                      className=\"mt-2 h-10\"\n                    />\n                  </div>\n                  <div>\n                    <Label>Background Color</Label>\n                    <Input type=\"color\" defaultValue=\"#FFFFFF\" className=\"mt-2 h-10\" />\n                  </div>\n                  <div>\n                    <Label>Text Color</Label>\n                    <Input type=\"color\" defaultValue=\"#000000\" className=\"mt-2 h-10\" />\n                  </div>\n                </TabsContent>\n\n                <TabsContent value=\"typography\" className=\"space-y-4\">\n                  <div>\n                    <Label>Font Family</Label>\n                    <select className=\"w-full mt-2 p-2 border rounded-md\">\n                      <option>Inter</option>\n                      <option>Roboto</option>\n                      <option>Open Sans</option>\n                      <option>Lato</option>\n                    </select>\n                  </div>\n                  <div>\n                    <Label>Font Size: {fontSize[0]}px</Label>\n                    <Slider\n                      value={fontSize}\n                      onValueChange={setFontSize}\n                      max={24}\n                      min={12}\n                      step={1}\n                      className=\"mt-2\"\n                    />\n                  </div>\n                  <div>\n                    <Label>Line Height</Label>\n                    <Slider\n                      defaultValue={[1.5]}\n                      max={2}\n                      min={1}\n                      step={0.1}\n                      className=\"mt-2\"\n                    />\n                  </div>\n                </TabsContent>\n\n                <TabsContent value=\"layout\" className=\"space-y-4\">\n                  <div>\n                    <Label>Container Width</Label>\n                    <select className=\"w-full mt-2 p-2 border rounded-md\">\n                      <option>Full Width</option>\n                      <option>1200px</option>\n                      <option>1024px</option>\n                      <option>768px</option>\n                    </select>\n                  </div>\n                  <div>\n                    <Label>Spacing: {spacing[0]}px</Label>\n                    <Slider\n                      value={spacing}\n                      onValueChange={setSpacing}\n                      max={32}\n                      min={0}\n                      step={4}\n                      className=\"mt-2\"\n                    />\n                  </div>\n                  <div>\n                    <Label>Border Radius</Label>\n                    <Slider\n                      defaultValue={[8]}\n                      max={20}\n                      min={0}\n                      step={2}\n                      className=\"mt-2\"\n                    />\n                  </div>\n                </TabsContent>\n\n                <TabsContent value=\"media\" className=\"space-y-4\">\n                  <div>\n                    <Label>Upload Image</Label>\n                    <Input type=\"file\" accept=\"image/*\" className=\"mt-2\" />\n                  </div>\n                  <div>\n                    <Label>Image Position</Label>\n                    <select className=\"w-full mt-2 p-2 border rounded-md\">\n                      <option>Center</option>\n                      <option>Top</option>\n                      <option>Bottom</option>\n                      <option>Left</option>\n                      <option>Right</option>\n                    </select>\n                  </div>\n                  <div>\n                    <Label>Image Size</Label>\n                    <select className=\"w-full mt-2 p-2 border rounded-md\">\n                      <option>Cover</option>\n                      <option>Contain</option>\n                      <option>Auto</option>\n                    </select>\n                  </div>\n                </TabsContent>\n              </Tabs>\n            </CardContent>\n          </Card>\n        </div>\n\n        {/* Live Preview */}\n        <div className=\"lg:col-span-3\">\n          <Card>\n            <CardHeader>\n              <CardTitle className=\"text-lg\">Live Preview</CardTitle>\n              <CardDescription>\n                See your changes in real-time\n              </CardDescription>\n            </CardHeader>\n            <CardContent>\n              <div \n                className=\"min-h-[600px] border-2 border-dashed border-muted-foreground/25 rounded-lg p-8\"\n                style={{ \n                  backgroundColor: \"#FFFFFF\",\n                  fontSize: `${fontSize[0]}px`,\n                  padding: `${spacing[0] * 2}px`\n                }}\n              >\n                <div className=\"space-y-6\">\n                  <div \n                    className=\"text-center p-6 rounded-lg\"\n                    style={{ backgroundColor: selectedColor, color: \"white\" }}\n                  >\n                    <h1 className=\"text-3xl font-bold mb-2\">Your Template Title</h1>\n                    <p className=\"text-lg opacity-90\">\n                      This is your customizable template preview\n                    </p>\n                  </div>\n                  \n                  <div className=\"grid md:grid-cols-2 gap-6\">\n                    <div className=\"space-y-4\">\n                      <h2 className=\"text-2xl font-semibold\">Section Title</h2>\n                      <p className=\"text-muted-foreground\">\n                        This is sample content that will update as you customize your template. \n                        You can see how your color and typography changes affect the overall design.\n                      </p>\n                      <Button style={{ backgroundColor: selectedColor }}>\n                        Call to Action\n                      </Button>\n                    </div>\n                    \n                    <div className=\"bg-muted rounded-lg p-6 flex items-center justify-center\">\n                      <div className=\"text-center text-muted-foreground\">\n                        <ImageIcon className=\"h-12 w-12 mx-auto mb-2\" />\n                        <p>Image Placeholder</p>\n                      </div>\n                    </div>\n                  </div>\n                  \n                  <div className=\"grid md:grid-cols-3 gap-4\">\n                    {[1, 2, 3].map((i) => (\n                      <div key={i} className=\"border rounded-lg p-4\">\n                        <h3 className=\"font-semibold mb-2\">Feature {i}</h3>\n                        <p className=\"text-sm text-muted-foreground\">\n                          Description of feature {i} goes here.\n                        </p>\n                      </div>\n                    ))}\n                  </div>\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n        </div>\n      </div>\n\n      {/* Export Options */}\n      <Card>\n        <CardHeader>\n          <CardTitle>Export Options</CardTitle>\n          <CardDescription>\n            Download your customized template\n          </CardDescription>\n        </CardHeader>\n        <CardContent>\n          <div className=\"flex gap-4\">\n            <Button>\n              <Download className=\"h-4 w-4 mr-2\" />\n              Download HTML/CSS\n            </Button>\n            <Button variant=\"outline\">\n              <Download className=\"h-4 w-4 mr-2\" />\n              Download React Components\n            </Button>\n            <Button variant=\"outline\">\n              <Download className=\"h-4 w-4 mr-2\" />\n              Export as PDF\n            </Button>\n          </div>\n        </CardContent>\n      </Card>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AATA;;;;;;;;;;AAqBe,SAAS;IACtB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAAC;KAAG;IAC7C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAAC;KAAE;IAE1C,MAAM,eAAe;QACnB;QAAW;QAAW;QAAW;QACjC;QAAW;QAAW;QAAW;KAClC;IAED,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;0CAAoC;;;;;;0CAClD,8OAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;kCAIvC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,kIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAU,MAAK;;kDAC7B,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAGnC,8OAAC,kIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAU,MAAK;;kDAC7B,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAGnC,8OAAC,kIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAU,MAAK;;kDAC7B,8OAAC,gMAAA,CAAA,MAAG;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAGlC,8OAAC,kIAAA,CAAA,SAAM;gCAAC,MAAK;;kDACX,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;0BAMvC,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,gIAAA,CAAA,OAAI;;8CACH,8OAAC,gIAAA,CAAA,aAAU;;sDACT,8OAAC,gIAAA,CAAA,YAAS;4CAAC,WAAU;sDAAU;;;;;;sDAC/B,8OAAC,gIAAA,CAAA,kBAAe;sDAAC;;;;;;;;;;;;8CAInB,8OAAC,gIAAA,CAAA,cAAW;8CACV,cAAA,8OAAC,gIAAA,CAAA,OAAI;wCAAC,cAAa;wCAAS,WAAU;;0DACpC,8OAAC,gIAAA,CAAA,WAAQ;gDAAC,WAAU;;kEAClB,8OAAC,gIAAA,CAAA,cAAW;wDAAC,OAAM;wDAAS,WAAU;kEACpC,cAAA,8OAAC,wMAAA,CAAA,UAAO;4DAAC,WAAU;;;;;;;;;;;kEAErB,8OAAC,gIAAA,CAAA,cAAW;wDAAC,OAAM;wDAAa,WAAU;kEACxC,cAAA,8OAAC,kMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;;;;;;kEAElB,8OAAC,gIAAA,CAAA,cAAW;wDAAC,OAAM;wDAAS,WAAU;kEACpC,cAAA,8OAAC,qNAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;;;;;;kEAEpB,8OAAC,gIAAA,CAAA,cAAW;wDAAC,OAAM;wDAAQ,WAAU;kEACnC,cAAA,8OAAC,oMAAA,CAAA,QAAS;4DAAC,WAAU;;;;;;;;;;;;;;;;;0DAIzB,8OAAC,gIAAA,CAAA,cAAW;gDAAC,OAAM;gDAAS,WAAU;;kEACpC,8OAAC;;0EACC,8OAAC,iIAAA,CAAA,QAAK;0EAAC;;;;;;0EACP,8OAAC;gEAAI,WAAU;0EACZ,aAAa,GAAG,CAAC,CAAC,sBACjB,8OAAC;wEAEC,WAAU;wEACV,OAAO;4EAAE,iBAAiB;wEAAM;wEAChC,SAAS,IAAM,iBAAiB;uEAH3B;;;;;;;;;;0EAOX,8OAAC,iIAAA,CAAA,QAAK;gEACJ,MAAK;gEACL,OAAO;gEACP,UAAU,CAAC,IAAM,iBAAiB,EAAE,MAAM,CAAC,KAAK;gEAChD,WAAU;;;;;;;;;;;;kEAGd,8OAAC;;0EACC,8OAAC,iIAAA,CAAA,QAAK;0EAAC;;;;;;0EACP,8OAAC,iIAAA,CAAA,QAAK;gEAAC,MAAK;gEAAQ,cAAa;gEAAU,WAAU;;;;;;;;;;;;kEAEvD,8OAAC;;0EACC,8OAAC,iIAAA,CAAA,QAAK;0EAAC;;;;;;0EACP,8OAAC,iIAAA,CAAA,QAAK;gEAAC,MAAK;gEAAQ,cAAa;gEAAU,WAAU;;;;;;;;;;;;;;;;;;0DAIzD,8OAAC,gIAAA,CAAA,cAAW;gDAAC,OAAM;gDAAa,WAAU;;kEACxC,8OAAC;;0EACC,8OAAC,iIAAA,CAAA,QAAK;0EAAC;;;;;;0EACP,8OAAC;gEAAO,WAAU;;kFAChB,8OAAC;kFAAO;;;;;;kFACR,8OAAC;kFAAO;;;;;;kFACR,8OAAC;kFAAO;;;;;;kFACR,8OAAC;kFAAO;;;;;;;;;;;;;;;;;;kEAGZ,8OAAC;;0EACC,8OAAC,iIAAA,CAAA,QAAK;;oEAAC;oEAAY,QAAQ,CAAC,EAAE;oEAAC;;;;;;;0EAC/B,8OAAC,kIAAA,CAAA,SAAM;gEACL,OAAO;gEACP,eAAe;gEACf,KAAK;gEACL,KAAK;gEACL,MAAM;gEACN,WAAU;;;;;;;;;;;;kEAGd,8OAAC;;0EACC,8OAAC,iIAAA,CAAA,QAAK;0EAAC;;;;;;0EACP,8OAAC,kIAAA,CAAA,SAAM;gEACL,cAAc;oEAAC;iEAAI;gEACnB,KAAK;gEACL,KAAK;gEACL,MAAM;gEACN,WAAU;;;;;;;;;;;;;;;;;;0DAKhB,8OAAC,gIAAA,CAAA,cAAW;gDAAC,OAAM;gDAAS,WAAU;;kEACpC,8OAAC;;0EACC,8OAAC,iIAAA,CAAA,QAAK;0EAAC;;;;;;0EACP,8OAAC;gEAAO,WAAU;;kFAChB,8OAAC;kFAAO;;;;;;kFACR,8OAAC;kFAAO;;;;;;kFACR,8OAAC;kFAAO;;;;;;kFACR,8OAAC;kFAAO;;;;;;;;;;;;;;;;;;kEAGZ,8OAAC;;0EACC,8OAAC,iIAAA,CAAA,QAAK;;oEAAC;oEAAU,OAAO,CAAC,EAAE;oEAAC;;;;;;;0EAC5B,8OAAC,kIAAA,CAAA,SAAM;gEACL,OAAO;gEACP,eAAe;gEACf,KAAK;gEACL,KAAK;gEACL,MAAM;gEACN,WAAU;;;;;;;;;;;;kEAGd,8OAAC;;0EACC,8OAAC,iIAAA,CAAA,QAAK;0EAAC;;;;;;0EACP,8OAAC,kIAAA,CAAA,SAAM;gEACL,cAAc;oEAAC;iEAAE;gEACjB,KAAK;gEACL,KAAK;gEACL,MAAM;gEACN,WAAU;;;;;;;;;;;;;;;;;;0DAKhB,8OAAC,gIAAA,CAAA,cAAW;gDAAC,OAAM;gDAAQ,WAAU;;kEACnC,8OAAC;;0EACC,8OAAC,iIAAA,CAAA,QAAK;0EAAC;;;;;;0EACP,8OAAC,iIAAA,CAAA,QAAK;gEAAC,MAAK;gEAAO,QAAO;gEAAU,WAAU;;;;;;;;;;;;kEAEhD,8OAAC;;0EACC,8OAAC,iIAAA,CAAA,QAAK;0EAAC;;;;;;0EACP,8OAAC;gEAAO,WAAU;;kFAChB,8OAAC;kFAAO;;;;;;kFACR,8OAAC;kFAAO;;;;;;kFACR,8OAAC;kFAAO;;;;;;kFACR,8OAAC;kFAAO;;;;;;kFACR,8OAAC;kFAAO;;;;;;;;;;;;;;;;;;kEAGZ,8OAAC;;0EACC,8OAAC,iIAAA,CAAA,QAAK;0EAAC;;;;;;0EACP,8OAAC;gEAAO,WAAU;;kFAChB,8OAAC;kFAAO;;;;;;kFACR,8OAAC;kFAAO;;;;;;kFACR,8OAAC;kFAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAUtB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,gIAAA,CAAA,OAAI;;8CACH,8OAAC,gIAAA,CAAA,aAAU;;sDACT,8OAAC,gIAAA,CAAA,YAAS;4CAAC,WAAU;sDAAU;;;;;;sDAC/B,8OAAC,gIAAA,CAAA,kBAAe;sDAAC;;;;;;;;;;;;8CAInB,8OAAC,gIAAA,CAAA,cAAW;8CACV,cAAA,8OAAC;wCACC,WAAU;wCACV,OAAO;4CACL,iBAAiB;4CACjB,UAAU,GAAG,QAAQ,CAAC,EAAE,CAAC,EAAE,CAAC;4CAC5B,SAAS,GAAG,OAAO,CAAC,EAAE,GAAG,EAAE,EAAE,CAAC;wCAChC;kDAEA,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,WAAU;oDACV,OAAO;wDAAE,iBAAiB;wDAAe,OAAO;oDAAQ;;sEAExD,8OAAC;4DAAG,WAAU;sEAA0B;;;;;;sEACxC,8OAAC;4DAAE,WAAU;sEAAqB;;;;;;;;;;;;8DAKpC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAG,WAAU;8EAAyB;;;;;;8EACvC,8OAAC;oEAAE,WAAU;8EAAwB;;;;;;8EAIrC,8OAAC,kIAAA,CAAA,SAAM;oEAAC,OAAO;wEAAE,iBAAiB;oEAAc;8EAAG;;;;;;;;;;;;sEAKrD,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,oMAAA,CAAA,QAAS;wEAAC,WAAU;;;;;;kFACrB,8OAAC;kFAAE;;;;;;;;;;;;;;;;;;;;;;;8DAKT,8OAAC;oDAAI,WAAU;8DACZ;wDAAC;wDAAG;wDAAG;qDAAE,CAAC,GAAG,CAAC,CAAC,kBACd,8OAAC;4DAAY,WAAU;;8EACrB,8OAAC;oEAAG,WAAU;;wEAAqB;wEAAS;;;;;;;8EAC5C,8OAAC;oEAAE,WAAU;;wEAAgC;wEACnB;wEAAE;;;;;;;;2DAHpB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAgB1B,8OAAC,gIAAA,CAAA,OAAI;;kCACH,8OAAC,gIAAA,CAAA,aAAU;;0CACT,8OAAC,gIAAA,CAAA,YAAS;0CAAC;;;;;;0CACX,8OAAC,gIAAA,CAAA,kBAAe;0CAAC;;;;;;;;;;;;kCAInB,8OAAC,gIAAA,CAAA,cAAW;kCACV,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kIAAA,CAAA,SAAM;;sDACL,8OAAC,0MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;8CAGvC,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAQ;;sDACd,8OAAC,0MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;8CAGvC,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAQ;;sDACd,8OAAC,0MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQnD", "debugId": null}}]}