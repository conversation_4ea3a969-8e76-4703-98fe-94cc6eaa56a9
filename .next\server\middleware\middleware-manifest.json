{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_9bbbbc5f._.js", "server/edge/chunks/[root-of-the-server]__c6c91fdf._.js", "server/edge/chunks/edge-wrapper_c7ec3fbd.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*){(\\\\.json)}?", "originalSource": "/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "903ou+si8jPmiJ9R1U8PjxHzUt1mwAm+hUYuJyHsngs=", "__NEXT_PREVIEW_MODE_ID": "8374f30588642012203406593f279364", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "c4e33c535d2df7b9d394e63ad22848b1cccaa221a7990221d2ff20e56155484f", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "3130c43e17410ae0c8f8875413d2db44b6b38ebf0127f3dcb62292f49505f435"}}}, "instrumentation": null, "functions": {}}