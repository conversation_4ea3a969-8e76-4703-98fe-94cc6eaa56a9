{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_061ca3cf._.js", "server/edge/chunks/[root-of-the-server]__c6c91fdf._.js", "server/edge/chunks/edge-wrapper_11b6cdf1.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*){(\\\\.json)}?", "originalSource": "/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "903ou+si8jPmiJ9R1U8PjxHzUt1mwAm+hUYuJyHsngs=", "__NEXT_PREVIEW_MODE_ID": "81edfc173fc3dcc47e0054d9ed0982a7", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "97d498670867a41f5998b9c04a24a65f2901cac6009727d7190dea8e9ab6577f", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "1582f5bdc8aa5aae8aca92d6e7c28f1304583dcb5768bb67fda088c05f916061"}}}, "instrumentation": null, "functions": {}}