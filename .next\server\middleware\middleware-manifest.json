{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_061ca3cf._.js", "server/edge/chunks/[root-of-the-server]__c6c91fdf._.js", "server/edge/chunks/edge-wrapper_11b6cdf1.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*){(\\\\.json)}?", "originalSource": "/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "903ou+si8jPmiJ9R1U8PjxHzUt1mwAm+hUYuJyHsngs=", "__NEXT_PREVIEW_MODE_ID": "c66579d644d83d1bf19d309a3bb4b3ba", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "4ffc1157d56cba97a3281202dddde5d74a9f35d707110448e2b9f419b8a956e2", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "bf68ba6ca831ce956a2fca629f3886b89ca3f5fa2a96b341a39a0ae0edf80c2a"}}}, "instrumentation": null, "functions": {}}