import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'

export async function POST(request: NextRequest) {
  try {
    const {
      path,
      userAgent,
      referrer,
      screenResolution,
      language,
      timezone,
      sessionId,
      deviceType,
      browser,
      os,
      country,
      city,
      pageTitle,
      loadTime,
      connectionType
    } = await request.json()

    // Get IP address from request headers
    const forwarded = request.headers.get('x-forwarded-for')
    const realIp = request.headers.get('x-real-ip')
    const ip = forwarded?.split(',')[0] || realIp || request.ip || 'unknown'

    // Get additional headers
    const acceptLanguage = request.headers.get('accept-language')
    const refererHeader = request.headers.get('referer')

    const supabase = await createClient()

    const { error } = await supabase
      .from('visitor_logs')
      .insert({
        ip_address: ip,
        path,
        user_agent: userAgent,
        referrer: referrer || refererHeader,
        screen_resolution: screenResolution,
        language: language || acceptLanguage?.split(',')[0],
        timezone,
        session_id: sessionId,
        device_type: deviceType,
        browser,
        operating_system: os,
        country,
        city,
        page_title: pageTitle,
        load_time: loadTime,
        connection_type: connectionType,
      })

    if (error) {
      console.error('Error tracking visitor:', error)
      return NextResponse.json(
        { error: 'Failed to track visitor' },
        { status: 500 }
      )
    }

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Error in visitor tracking:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
