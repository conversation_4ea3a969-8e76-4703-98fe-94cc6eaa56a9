import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'

export async function POST(request: NextRequest) {
  try {
    const { path, userAgent } = await request.json()
    
    // Get IP address from request headers
    const forwarded = request.headers.get('x-forwarded-for')
    const realIp = request.headers.get('x-real-ip')
    const ip = forwarded?.split(',')[0] || realIp || request.ip || 'unknown'

    const supabase = await createClient()
    
    const { error } = await supabase
      .from('visitor_logs')
      .insert({
        ip_address: ip,
        path,
        user_agent: userAgent,
      })

    if (error) {
      console.error('Error tracking visitor:', error)
      return NextResponse.json(
        { error: 'Failed to track visitor' },
        { status: 500 }
      )
    }

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Error in visitor tracking:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
