import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"

export default function PaymentsPage() {
  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Payments</h1>
        <p className="text-muted-foreground">
          Process payments and manage payment methods
        </p>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>Quick Payment</CardTitle>
            <CardDescription>
              Create a new payment quickly
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <label htmlFor="amount" className="text-sm font-medium">
                Amount (₹)
              </label>
              <Input 
                id="amount" 
                type="number" 
                placeholder="Enter amount" 
                min="1"
              />
            </div>
            <div className="space-y-2">
              <label htmlFor="description" className="text-sm font-medium">
                Description
              </label>
              <Input 
                id="description" 
                placeholder="Payment description" 
              />
            </div>
            <Button className="w-full">
              Create Payment
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Payment Methods</CardTitle>
            <CardDescription>
              Manage your saved payment methods
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="border rounded-lg p-4">
              <div className="flex justify-between items-center">
                <div>
                  <p className="font-medium">•••• •••• •••• 4242</p>
                  <p className="text-sm text-muted-foreground">Expires 12/25</p>
                </div>
                <Button variant="outline" size="sm">
                  Remove
                </Button>
              </div>
            </div>
            <Button variant="outline" className="w-full">
              Add New Payment Method
            </Button>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Recent Transactions</CardTitle>
          <CardDescription>
            Your recent payment activity
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[
              { id: "TXN-001", amount: "₹2,499", status: "Success", date: "2024-01-15" },
              { id: "TXN-002", amount: "₹999", status: "Pending", date: "2024-01-14" },
              { id: "TXN-003", amount: "₹4,999", status: "Success", date: "2024-01-13" },
            ].map((txn) => (
              <div key={txn.id} className="flex justify-between items-center p-4 border rounded-lg">
                <div>
                  <p className="font-medium">{txn.id}</p>
                  <p className="text-sm text-muted-foreground">{txn.date}</p>
                </div>
                <div className="text-right">
                  <p className="font-medium">{txn.amount}</p>
                  <p className={`text-sm ${txn.status === 'Success' ? 'text-green-600' : 'text-yellow-600'}`}>
                    {txn.status}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
