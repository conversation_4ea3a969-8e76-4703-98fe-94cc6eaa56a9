"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { createClient } from "@/lib/supabase/client"
import { Database } from "@/lib/database.types"
import { toast } from "sonner"
import { useRouter } from "next/navigation"
import { 
  Mail, 
  ShoppingBag, 
  Settings, 
  Eye, 
  Download, 
  Trash2, 
  ArrowUpDown,
  Users
} from "lucide-react"

type ContactRequest = Database['public']['Tables']['contact_requests']['Row']
type Purchase = Database['public']['Tables']['purchases']['Row'] & {
  templates: Database['public']['Tables']['templates']['Row']
  profiles: Database['public']['Tables']['profiles']['Row']
}
type Customization = Database['public']['Tables']['customizations']['Row'] & {
  profiles: Database['public']['Tables']['profiles']['Row']
}
type VisitorLog = Database['public']['Tables']['visitor_logs']['Row']

export default function AdminPage() {
  const [user, setUser] = useState<any>(null)
  const [isAdmin, setIsAdmin] = useState(false)
  const [loading, setLoading] = useState(true)
  const [contactRequests, setContactRequests] = useState<ContactRequest[]>([])
  const [purchases, setPurchases] = useState<Purchase[]>([])
  const [customizations, setCustomizations] = useState<Customization[]>([])
  const [visitorLogs, setVisitorLogs] = useState<VisitorLog[]>([])
  const [activeTab, setActiveTab] = useState("contacts")

  const supabase = createClient()
  const router = useRouter()

  useEffect(() => {
    checkAdminAccess()
  }, [])

  const checkAdminAccess = async () => {
    try {
      const { data: { user }, error: userError } = await supabase.auth.getUser()
      
      if (userError || !user) {
        router.push('/')
        return
      }

      setUser(user)

      // Check if user is admin
      const { data: profile, error: profileError } = await supabase
        .from('profiles')
        .select('role')
        .eq('id', user.id)
        .single()

      if (profileError || !profile || profile.role !== 'admin') {
        router.push('/')
        return
      }

      setIsAdmin(true)
      await loadAdminData()
    } catch (error) {
      console.error('Error checking admin access:', error)
      router.push('/')
    } finally {
      setLoading(false)
    }
  }

  const loadAdminData = async () => {
    try {
      await Promise.all([
        loadContactRequests(),
        loadPurchases(),
        loadCustomizations(),
        loadVisitorLogs()
      ])
    } catch (error) {
      console.error('Error loading admin data:', error)
      toast.error('Failed to load admin data')
    }
  }

  const loadContactRequests = async () => {
    const { data, error } = await supabase
      .from('contact_requests')
      .select('*')
      .order('created_at', { ascending: false })

    if (error) throw error
    setContactRequests(data || [])
  }

  const loadPurchases = async () => {
    const { data, error } = await supabase
      .from('purchases')
      .select(`
        *,
        templates (*),
        profiles (*)
      `)
      .order('created_at', { ascending: false })

    if (error) throw error
    setPurchases(data || [])
  }

  const loadCustomizations = async () => {
    const { data, error } = await supabase
      .from('customizations')
      .select(`
        *,
        profiles (*)
      `)
      .order('created_at', { ascending: false })

    if (error) throw error
    setCustomizations(data || [])
  }

  const loadVisitorLogs = async () => {
    const { data, error } = await supabase
      .from('visitor_logs')
      .select('*')
      .order('created_at', { ascending: false })
      .limit(1000) // Limit to recent 1000 logs

    if (error) throw error
    setVisitorLogs(data || [])
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-2">Loading Admin Panel...</h1>
          <p className="text-muted-foreground">Verifying admin access</p>
        </div>
      </div>
    )
  }

  if (!isAdmin) {
    return null // Will redirect to home
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Admin Panel</h1>
        <p className="text-muted-foreground">
          Manage your application data and analytics
        </p>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Contact Requests</CardTitle>
            <Mail className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{contactRequests.length}</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Purchases</CardTitle>
            <ShoppingBag className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{purchases.length}</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Customizations</CardTitle>
            <Settings className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{customizations.length}</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Visitor Logs</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{visitorLogs.length}</div>
          </CardContent>
        </Card>
      </div>

      {/* Admin Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="contacts">Contact Requests</TabsTrigger>
          <TabsTrigger value="purchases">Purchases</TabsTrigger>
          <TabsTrigger value="customizations">Customizations</TabsTrigger>
          <TabsTrigger value="visitors">Visitor Logs</TabsTrigger>
        </TabsList>

        <TabsContent value="contacts" className="space-y-4">
          <ContactRequestsTab 
            data={contactRequests} 
            onRefresh={loadContactRequests}
          />
        </TabsContent>

        <TabsContent value="purchases" className="space-y-4">
          <PurchasesTab 
            data={purchases} 
            onRefresh={loadPurchases}
          />
        </TabsContent>

        <TabsContent value="customizations" className="space-y-4">
          <CustomizationsTab 
            data={customizations} 
            onRefresh={loadCustomizations}
          />
        </TabsContent>

        <TabsContent value="visitors" className="space-y-4">
          <VisitorLogsTab 
            data={visitorLogs} 
            onRefresh={loadVisitorLogs}
          />
        </TabsContent>
      </Tabs>
    </div>
  )
}

// Contact Requests Tab Component
function ContactRequestsTab({ data, onRefresh }: { data: ContactRequest[], onRefresh: () => void }) {
  const supabase = createClient()
  const [sortField, setSortField] = useState<keyof ContactRequest>('created_at')
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc')

  const sortedData = [...data].sort((a, b) => {
    const aVal = a[sortField]
    const bVal = b[sortField]

    if (aVal < bVal) return sortDirection === 'asc' ? -1 : 1
    if (aVal > bVal) return sortDirection === 'asc' ? 1 : -1
    return 0
  })

  const handleSort = (field: keyof ContactRequest) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc')
    } else {
      setSortField(field)
      setSortDirection('asc')
    }
  }

  const handleDelete = async (id: string) => {
    if (!confirm('Are you sure you want to delete this contact request?')) return

    try {
      const { error } = await supabase
        .from('contact_requests')
        .delete()
        .eq('id', id)

      if (error) throw error

      toast.success('Contact request deleted successfully')
      onRefresh()
    } catch (error) {
      console.error('Error deleting contact request:', error)
      toast.error('Failed to delete contact request')
    }
  }

  const exportToCSV = () => {
    const headers = ['Name', 'Email', 'Message', 'Created At']
    const csvData = [
      headers,
      ...sortedData.map(item => [
        item.name,
        item.email,
        item.message,
        new Date(item.created_at).toLocaleString()
      ])
    ]

    const csvContent = csvData.map(row =>
      row.map(field => `"${field}"`).join(',')
    ).join('\n')

    const blob = new Blob([csvContent], { type: 'text/csv' })
    const url = window.URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `contact-requests-${new Date().toISOString().split('T')[0]}.csv`
    a.click()
    window.URL.revokeObjectURL(url)
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex justify-between items-center">
          <div>
            <CardTitle>Contact Requests</CardTitle>
            <CardDescription>Manage customer inquiries and messages</CardDescription>
          </div>
          <div className="flex gap-2">
            <Button variant="outline" onClick={exportToCSV}>
              <Download className="h-4 w-4 mr-2" />
              Export CSV
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {sortedData.map((request) => (
            <div key={request.id} className="border rounded-lg p-4">
              <div className="flex justify-between items-start mb-2">
                <div>
                  <h4 className="font-semibold">{request.name}</h4>
                  <p className="text-sm text-muted-foreground">{request.email}</p>
                </div>
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleSort('created_at')}
                  >
                    <ArrowUpDown className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="destructive"
                    size="sm"
                    onClick={() => handleDelete(request.id)}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
              <p className="text-sm mb-2">{request.message}</p>
              <p className="text-xs text-muted-foreground">
                {new Date(request.created_at).toLocaleString()}
              </p>
            </div>
          ))}
          {sortedData.length === 0 && (
            <div className="text-center py-8 text-muted-foreground">
              No contact requests found
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}

// Purchases Tab Component
function PurchasesTab({ data, onRefresh }: { data: Purchase[], onRefresh: () => void }) {
  const supabase = createClient()
  const [sortField, setSortField] = useState<keyof Purchase>('created_at')
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc')

  const sortedData = [...data].sort((a, b) => {
    const aVal = a[sortField]
    const bVal = b[sortField]

    if (aVal < bVal) return sortDirection === 'asc' ? -1 : 1
    if (aVal > bVal) return sortDirection === 'asc' ? 1 : -1
    return 0
  })

  const handleSort = (field: keyof Purchase) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc')
    } else {
      setSortField(field)
      setSortDirection('asc')
    }
  }

  const handleDelete = async (id: string) => {
    if (!confirm('Are you sure you want to delete this purchase record?')) return

    try {
      const { error } = await supabase
        .from('purchases')
        .delete()
        .eq('id', id)

      if (error) throw error

      toast.success('Purchase record deleted successfully')
      onRefresh()
    } catch (error) {
      console.error('Error deleting purchase:', error)
      toast.error('Failed to delete purchase record')
    }
  }

  const exportToCSV = () => {
    const headers = ['User Email', 'Template', 'Amount', 'Currency', 'Status', 'Payment ID', 'Created At']
    const csvData = [
      headers,
      ...sortedData.map(item => [
        item.profiles?.full_name || 'Unknown',
        item.templates?.title || 'Unknown Template',
        item.amount.toString(),
        item.currency,
        item.status,
        item.razorpay_payment_id,
        new Date(item.created_at).toLocaleString()
      ])
    ]

    const csvContent = csvData.map(row =>
      row.map(field => `"${field}"`).join(',')
    ).join('\n')

    const blob = new Blob([csvContent], { type: 'text/csv' })
    const url = window.URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `purchases-${new Date().toISOString().split('T')[0]}.csv`
    a.click()
    window.URL.revokeObjectURL(url)
  }

  const totalRevenue = sortedData.reduce((sum, purchase) => sum + purchase.amount, 0)

  return (
    <Card>
      <CardHeader>
        <div className="flex justify-between items-center">
          <div>
            <CardTitle>Purchases</CardTitle>
            <CardDescription>
              Total Revenue: ₹{totalRevenue} • {sortedData.length} purchases
            </CardDescription>
          </div>
          <div className="flex gap-2">
            <Button variant="outline" onClick={exportToCSV}>
              <Download className="h-4 w-4 mr-2" />
              Export CSV
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {sortedData.map((purchase) => (
            <div key={purchase.id} className="border rounded-lg p-4">
              <div className="flex justify-between items-start mb-2">
                <div>
                  <h4 className="font-semibold">{purchase.templates?.title}</h4>
                  <p className="text-sm text-muted-foreground">
                    {purchase.profiles?.full_name || 'Unknown User'}
                  </p>
                </div>
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleSort('amount')}
                  >
                    <ArrowUpDown className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="destructive"
                    size="sm"
                    onClick={() => handleDelete(purchase.id)}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                <div>
                  <span className="text-muted-foreground">Amount:</span>
                  <p className="font-medium">₹{purchase.amount}</p>
                </div>
                <div>
                  <span className="text-muted-foreground">Status:</span>
                  <Badge variant={purchase.status === 'completed' ? 'default' : 'secondary'}>
                    {purchase.status}
                  </Badge>
                </div>
                <div>
                  <span className="text-muted-foreground">Payment ID:</span>
                  <p className="font-mono text-xs">{purchase.razorpay_payment_id}</p>
                </div>
                <div>
                  <span className="text-muted-foreground">Date:</span>
                  <p>{new Date(purchase.created_at).toLocaleDateString()}</p>
                </div>
              </div>
            </div>
          ))}
          {sortedData.length === 0 && (
            <div className="text-center py-8 text-muted-foreground">
              No purchases found
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}

// Customizations Tab Component
function CustomizationsTab({ data, onRefresh }: { data: Customization[], onRefresh: () => void }) {
  const supabase = createClient()
  const [sortField, setSortField] = useState<keyof Customization>('created_at')
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc')

  const sortedData = [...data].sort((a, b) => {
    const aVal = a[sortField]
    const bVal = b[sortField]

    if (aVal < bVal) return sortDirection === 'asc' ? -1 : 1
    if (aVal > bVal) return sortDirection === 'asc' ? 1 : -1
    return 0
  })

  const handleSort = (field: keyof Customization) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc')
    } else {
      setSortField(field)
      setSortDirection('asc')
    }
  }

  const handleDelete = async (id: string) => {
    if (!confirm('Are you sure you want to delete this customization?')) return

    try {
      const { error } = await supabase
        .from('customizations')
        .delete()
        .eq('id', id)

      if (error) throw error

      toast.success('Customization deleted successfully')
      onRefresh()
    } catch (error) {
      console.error('Error deleting customization:', error)
      toast.error('Failed to delete customization')
    }
  }

  const exportToCSV = () => {
    const headers = ['User', 'Navbar Style', 'Hero Section', 'Footer Style', 'Created At', 'Updated At']
    const csvData = [
      headers,
      ...sortedData.map(item => [
        item.profiles?.full_name || 'Unknown',
        item.navbar_style,
        item.hero_section,
        item.footer_style,
        new Date(item.created_at).toLocaleString(),
        new Date(item.updated_at).toLocaleString()
      ])
    ]

    const csvContent = csvData.map(row =>
      row.map(field => `"${field}"`).join(',')
    ).join('\n')

    const blob = new Blob([csvContent], { type: 'text/csv' })
    const url = window.URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `customizations-${new Date().toISOString().split('T')[0]}.csv`
    a.click()
    window.URL.revokeObjectURL(url)
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex justify-between items-center">
          <div>
            <CardTitle>Customizations</CardTitle>
            <CardDescription>User template customization sessions</CardDescription>
          </div>
          <div className="flex gap-2">
            <Button variant="outline" onClick={exportToCSV}>
              <Download className="h-4 w-4 mr-2" />
              Export CSV
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {sortedData.map((customization) => (
            <div key={customization.id} className="border rounded-lg p-4">
              <div className="flex justify-between items-start mb-2">
                <div>
                  <h4 className="font-semibold">
                    {customization.profiles?.full_name || 'Unknown User'}
                  </h4>
                  <p className="text-sm text-muted-foreground">
                    ID: {customization.id.slice(0, 8)}...
                  </p>
                </div>
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleSort('created_at')}
                  >
                    <ArrowUpDown className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="destructive"
                    size="sm"
                    onClick={() => handleDelete(customization.id)}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                <div>
                  <span className="text-muted-foreground">Navbar:</span>
                  <p className="font-medium">{customization.navbar_style}</p>
                </div>
                <div>
                  <span className="text-muted-foreground">Hero:</span>
                  <p className="font-medium">{customization.hero_section}</p>
                </div>
                <div>
                  <span className="text-muted-foreground">Footer:</span>
                  <p className="font-medium">{customization.footer_style}</p>
                </div>
              </div>
              <div className="mt-2 text-xs text-muted-foreground">
                Created: {new Date(customization.created_at).toLocaleString()} •
                Updated: {new Date(customization.updated_at).toLocaleString()}
              </div>
            </div>
          ))}
          {sortedData.length === 0 && (
            <div className="text-center py-8 text-muted-foreground">
              No customizations found
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}

// Visitor Logs Tab Component
function VisitorLogsTab({ data, onRefresh }: { data: VisitorLog[], onRefresh: () => void }) {
  const supabase = createClient()
  const [sortField, setSortField] = useState<keyof VisitorLog>('created_at')
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc')

  const sortedData = [...data].sort((a, b) => {
    const aVal = a[sortField]
    const bVal = b[sortField]

    if (aVal < bVal) return sortDirection === 'asc' ? -1 : 1
    if (aVal > bVal) return sortDirection === 'asc' ? 1 : -1
    return 0
  })

  const handleSort = (field: keyof VisitorLog) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc')
    } else {
      setSortField(field)
      setSortDirection('asc')
    }
  }

  const handleDelete = async (id: string) => {
    if (!confirm('Are you sure you want to delete this visitor log?')) return

    try {
      const { error } = await supabase
        .from('visitor_logs')
        .delete()
        .eq('id', id)

      if (error) throw error

      toast.success('Visitor log deleted successfully')
      onRefresh()
    } catch (error) {
      console.error('Error deleting visitor log:', error)
      toast.error('Failed to delete visitor log')
    }
  }

  const exportToCSV = () => {
    const headers = ['IP Address', 'Path', 'User Agent', 'Created At']
    const csvData = [
      headers,
      ...sortedData.map(item => [
        item.ip_address || 'Unknown',
        item.path,
        item.user_agent || 'Unknown',
        new Date(item.created_at).toLocaleString()
      ])
    ]

    const csvContent = csvData.map(row =>
      row.map(field => `"${field}"`).join(',')
    ).join('\n')

    const blob = new Blob([csvContent], { type: 'text/csv' })
    const url = window.URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `visitor-logs-${new Date().toISOString().split('T')[0]}.csv`
    a.click()
    window.URL.revokeObjectURL(url)
  }

  const clearOldLogs = async () => {
    if (!confirm('Are you sure you want to delete logs older than 30 days?')) return

    try {
      const thirtyDaysAgo = new Date()
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)

      const { error } = await supabase
        .from('visitor_logs')
        .delete()
        .lt('created_at', thirtyDaysAgo.toISOString())

      if (error) throw error

      toast.success('Old visitor logs cleared successfully')
      onRefresh()
    } catch (error) {
      console.error('Error clearing old logs:', error)
      toast.error('Failed to clear old logs')
    }
  }

  // Analytics
  const uniqueIPs = new Set(sortedData.map(log => log.ip_address)).size
  const topPaths = sortedData.reduce((acc, log) => {
    acc[log.path] = (acc[log.path] || 0) + 1
    return acc
  }, {} as Record<string, number>)

  const topPathsArray = Object.entries(topPaths)
    .sort(([,a], [,b]) => b - a)
    .slice(0, 5)

  return (
    <Card>
      <CardHeader>
        <div className="flex justify-between items-center">
          <div>
            <CardTitle>Visitor Logs</CardTitle>
            <CardDescription>
              {sortedData.length} visits • {uniqueIPs} unique IPs
            </CardDescription>
          </div>
          <div className="flex gap-2">
            <Button variant="outline" onClick={clearOldLogs}>
              <Trash2 className="h-4 w-4 mr-2" />
              Clear Old
            </Button>
            <Button variant="outline" onClick={exportToCSV}>
              <Download className="h-4 w-4 mr-2" />
              Export CSV
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        {/* Top Paths Analytics */}
        <div className="mb-6 p-4 bg-muted rounded-lg">
          <h4 className="font-semibold mb-2">Top Visited Pages</h4>
          <div className="space-y-1">
            {topPathsArray.map(([path, count]) => (
              <div key={path} className="flex justify-between text-sm">
                <span>{path}</span>
                <span className="font-medium">{count} visits</span>
              </div>
            ))}
          </div>
        </div>

        <div className="space-y-4">
          {sortedData.map((log) => (
            <div key={log.id} className="border rounded-lg p-4">
              <div className="flex justify-between items-start mb-2">
                <div>
                  <h4 className="font-semibold">{log.path}</h4>
                  <p className="text-sm text-muted-foreground">
                    IP: {log.ip_address || 'Unknown'}
                  </p>
                </div>
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleSort('created_at')}
                  >
                    <ArrowUpDown className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="destructive"
                    size="sm"
                    onClick={() => handleDelete(log.id)}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
              <div className="text-sm">
                <p className="text-muted-foreground mb-1">User Agent:</p>
                <p className="font-mono text-xs break-all">
                  {log.user_agent || 'Unknown'}
                </p>
              </div>
              <div className="mt-2 text-xs text-muted-foreground">
                {new Date(log.created_at).toLocaleString()}
              </div>
            </div>
          ))}
          {sortedData.length === 0 && (
            <div className="text-center py-8 text-muted-foreground">
              No visitor logs found
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}
