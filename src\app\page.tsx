import { Card, CardContent, CardDescription, Card<PERSON>eader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { ArrowRight, Palette, FileText, Zap } from "lucide-react"
import Link from "next/link"

export default function Home() {
  return (
    <div className="space-y-8">
      {/* Hero Section */}
      <div className="text-center space-y-4">
        <h1 className="text-4xl font-bold tracking-tight">
          Welcome to KaleidoneX
        </h1>
        <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
          Create stunning, customizable templates with our powerful design tools.
          Build beautiful websites and applications with ease.
        </p>
        <div className="flex gap-4 justify-center">
          <Link href="/templates">
            <Button size="lg">
              Browse Templates
              <ArrowRight className="ml-2 h-4 w-4" />
            </Button>
          </Link>
          <Link href="/customize">
            <Button variant="outline" size="lg">
              Start Customizing
            </Button>
          </Link>
        </div>
      </div>

      {/* Features Grid */}
      <div className="grid gap-6 md:grid-cols-3">
        <Card>
          <CardHeader>
            <div className="flex items-center gap-2">
              <FileText className="h-5 w-5 text-primary" />
              <CardTitle>Rich Templates</CardTitle>
            </div>
            <CardDescription>
              Choose from a wide variety of professionally designed templates
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground">
              Our template library includes designs for every industry and use case.
              From business websites to creative portfolios, find the perfect starting point.
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <div className="flex items-center gap-2">
              <Palette className="h-5 w-5 text-primary" />
              <CardTitle>Live Customization</CardTitle>
            </div>
            <CardDescription>
              Customize your templates in real-time with our visual editor
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground">
              See your changes instantly as you customize colors, fonts, layouts, and content.
              No coding required - just point, click, and create.
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <div className="flex items-center gap-2">
              <Zap className="h-5 w-5 text-primary" />
              <CardTitle>Fast & Modern</CardTitle>
            </div>
            <CardDescription>
              Built with the latest technologies for optimal performance
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground">
              Powered by Next.js, Tailwind CSS, and modern web standards.
              Your templates will be fast, responsive, and SEO-friendly.
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Stats Section */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardContent className="p-6 text-center">
            <div className="text-2xl font-bold">50+</div>
            <p className="text-sm text-muted-foreground">Templates</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6 text-center">
            <div className="text-2xl font-bold">1000+</div>
            <p className="text-sm text-muted-foreground">Happy Users</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6 text-center">
            <div className="text-2xl font-bold">24/7</div>
            <p className="text-sm text-muted-foreground">Support</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6 text-center">
            <div className="text-2xl font-bold">99.9%</div>
            <p className="text-sm text-muted-foreground">Uptime</p>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
